<template>
  <div style="margin-bottom: 40px">
    <CommonTitle text="指挥体系">
      <div class="zhddTitleBar">
        <img
          v-if="manageUrl != ''"
          src="@/assets/common/edit.png"
          alt=""
          style="margin-left: 10px; cursor: pointer"
          @click="openManage()"
        />
      </div>
    </CommonTitle>
    <div class="tabChange">
      <div
        class="tabItem"
        v-for="(item, index) in tab"
        :class="{ tabActive: activeIndex === index }"
        @click="changeTab(item, index)"
      >
        {{ item }}
      </div>
    </div>
    <div class="zhtx">
      <div class="table">
        <div class="th">
          <div class="th_td" style="flex: 0.25; text-align: center">
            {{ activeItem == '乡镇街道指挥中心' ? '乡镇街道' : '部门' }}
          </div>
          <div class="th_td" style="flex: 0.15; text-align: left">职务</div>
          <div class="th_td" style="flex: 0.15; text-align: center">姓名</div>
          <div class="th_td" style="flex: 0.4; text-align: center">联系方式</div>
        </div>
        <div class="tbody" id="box2" @mouseenter="mouseenterEvent2" @mouseleave="mouseleaveEvent2">
          <div v-show="activeItem == '市直部门'">
            <div class="tr" v-for="(item, index) in szbmTable" :key="index">
              <div class="tr_td" style="flex: 0.25; text-align: center" :title="item.bm">
                {{ item.bm }}
              </div>
              <div class="tr_td" style="flex: 0.15; text-align: center">
                {{ item.zw }}
              </div>
              <div class="tr_td" style="flex: 0.15; text-align: center">
                {{ item.name }}
              </div>
              <div class="tr_td" style="flex: 0.4; text-align: center">
                <div style="display: flex; justify-content: space-around; align-items: center">
                  <div>{{ toPhone(item.phone) }}</div>
                  <span class="Phoneicon" @click="openCall(item.phone)"></span>
                  <span class="Videoicon" @click="openVideo(item.phone)"></span>
                </div>
              </div>
            </div>
          </div>
          <div v-show="activeItem == '县市区指挥中心'">
            <div class="tr" v-for="(item, index) in xsqTableData" :key="index">
              <div class="tr_td" style="flex: 0.25; text-align: center" :title="item.bm">
                {{ item.bm }}
              </div>
              <div class="tr_td" style="flex: 0.15; text-align: center">
                {{ item.zw }}
              </div>
              <div class="tr_td" style="flex: 0.15; text-align: center">
                {{ item.name }}
              </div>
              <div class="tr_td" style="flex: 0.4; text-align: center">
                <div style="display: flex; justify-content: space-around; align-items: center">
                  <div>{{ toPhone(item.phone) }}</div>
                  <span class="Phoneicon" @click="openCall(item.phone)"></span>
                  <span class="Videoicon" @click="openVideo(item.phone)"></span>
                </div>
              </div>
            </div>
          </div>
          <div v-show="activeItem == '乡镇街道指挥中心'">
            <div class="tr" v-for="(item, index) in xzjdTableData" :key="index">
              <div class="tr_td" style="flex: 0.25; text-align: center" :title="item.bm">
                {{ item.bm }}
              </div>
              <div class="tr_td" style="flex: 0.15; text-align: center">
                {{ item.zw }}
              </div>
              <div class="tr_td" style="flex: 0.15; text-align: center">
                {{ item.name }}
              </div>
              <div class="tr_td" style="flex: 0.4; text-align: center">
                <div style="display: flex; justify-content: space-around; align-items: center">
                  <div>{{ toPhone(item.phone) }}</div>
                  <span class="Phoneicon" @click="openCall(item.phone)"></span>
                  <span class="Videoicon" @click="openVideo(item.phone)"></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      v-if="dialogVisible5"
      title="语音通话"
      :visible.sync="dialogVisible5"
      :modal-append-to-body="false"
      :append-to-body="false"
      :show-close="false"
      width="836px"
      top="45vh"
      custom-class="call_dialog"
    >
      <div class="tel_box">
        <div class="tel_top">
          <img class="tel_header" src="@/assets/ajhf/phone_header.png" alt="" />
          <div class="info_box">
            <div class="tel">{{ phoneCode }}</div>
            <div class="status_box">
              <div class="status" :class="{ green_status: telStatus }">{{ telStatus ? '通话中' : '拨号中' }}</div>
              <img v-if="telStatus" class="status_img1" src="@/assets/ajhf/phone_audio.png" alt="" />
              <img v-else class="status_img2" src="@/assets/ajhf/phone_calling.png" alt="" />
            </div>
            <div class="voice_box">
              <img
                v-if="muteFlag"
                @click="changeMute(muteFlag)"
                class="voice_img"
                src="@/assets/ajhf/phone_novoice.png"
                alt=""
              />
              <img v-else class="voice_img" @click="changeMute(muteFlag)" src="@/assets/ajhf/phone_voice.png" alt="" />
              <div class="voice_time">{{ voiceTime }}</div>
            </div>
          </div>
        </div>
        <div class="tel_center">
          <img class="tel_end" @click="endCall" src="@/assets/ajhf/phone_end.png" alt="" />
        </div>
      </div>
    </el-dialog>
    <el-dialog
      v-if="dialogVisible6"
      title="视频通话"
      :visible.sync="dialogVisible6"
      :modal-append-to-body="false"
      :append-to-body="false"
      :show-close="false"
      width="836px"
      top="45vh"
      custom-class="call_dialog"
    >
      <div class="tel_box">
        <div class="tel_top">
          <div class="remoteView_box">
            <div class="remoteView" id="remoteView_cincc_5g"></div>
          </div>
          <div class="info_box">
            <div class="tel">{{ phoneCode }}</div>
            <div class="status_box">
              <div class="status" :class="{ green_status: telStatus }">{{ telStatus ? '通话中' : '视频中' }}</div>
              <img v-if="telStatus" class="status_img1" src="@/assets/ajhf/phone_audio.png" alt="" />
              <img v-else class="status_img2" src="@/assets/ajhf/phone_calling.png" alt="" />
            </div>
            <div class="voice_box">
              <img class="voice_img" src="@/assets/ajhf/phone_video.png" alt="" />
              <div class="voice_time">{{ voiceTime }}</div>
            </div>
            <div class="selfView_box">
              <div class="selfView" id="selfView_cincc_5g"></div>
            </div>
          </div>
        </div>
        <div class="tel_center">
          <img class="tel_end" @click="endCall" src="@/assets/ajhf/phone_end.png" alt="" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import { getZhtxList } from '@/api/zhdd'
export default {
  name: 'index',
  components: {
    CommonTitle,
  },
  data() {
    return {
      year: localStorage.getItem('year'),
      city: localStorage.getItem('city'),
      manageUrl: 'https://csdn.dsjj.jinhua.gov.cn:8303/dutyPersonnel',
      activeItem: '市直部门',
      activeIndex: 0,
      tab: ['市直部门', '县市区指挥中心'],
      dom2: null,
      time2: null,
      szbmTable: [],
      xsqTableData: [],
      xzjdTableData: [],
      //网络电话
      dialogVisible5: false,
      phoneCode: '',
      telStatus: false,
      muteFlag: false,
      voiceTime: '00:00:00',
      config: {
        tenantType: 1,
        ip: 'iccs.pointlinkprox.com',
        port: '9080',
        vccId: '100280',
        agentId: '5727',
        password: 'Zyzx@10086',
        loginKey: '3W4SS2MK1YJBBJHWQEWOSRFF',
        event: {
          OnInitalSuccess: this._OnInitalSuccess,
          OnInitalFailure: this._OnInitalFailure,
          OnAnswerCall: this._OnAnswerCall,
          OnCallEnd: this._OnCallEnd,
          OnReportBtnStatus: this._OnReportBtnStatus,
          OnCallRing: this._OnCallRing,
          OnBarExit: this._OnBarExit,
          OnUpdateVideoWindow: this._OnUpdateVideoWindow,
          OnAgentWorkReport: this._OnAgentWorkReport,
        },
      },
      VccBar: null,
      signIn: false,
      load: false,
      callTimer: null, // 通话计时器
      dialogVisible6: false,
    }
  },
  beforeDestroy() {
    clearInterval(this.callTimer)
  },
  computed: {},
  mounted() {
    this.$bus.$on('cityChange', (city) => {
      this.city = city
      this.initApi(city, localStorage.getItem('year'))
    })
    this.$bus.$on('yearChange', (year) => {
      this.year = year
      this.initApi(localStorage.getItem('city'), year)
    })
    this.initApi(localStorage.getItem('city'), localStorage.getItem('year'))
    // 表格滚动
    this.dom2 = document.getElementById('box2')
    this.mouseleaveEvent2()
    this.handleSignIn()
  },
  methods: {
    //网络通话开始
    _OnInitalSuccess() {
      this.signIn = true
      console.log('初始化成功')
    },
    _OnInitalFailure() {},
    _OnAnswerCall(userNo, answerTime, serialID, serviceDirect, callID, userParam, taskID, av, tc, haveAsrEvent) {
      this.telStatus = true
      console.log('serialID1111', serialID)
      // 创建计时器，每秒更新一次通话时间
      this.callTimer = setInterval(() => {
        let seconds = parseInt(this.voiceTime.split(':')[2]) + 1
        let minutes = parseInt(this.voiceTime.split(':')[1])
        let hours = parseInt(this.voiceTime.split(':')[0])

        if (seconds >= 60) {
          seconds = 0
          minutes++
        }
        if (minutes >= 60) {
          minutes = 0
          hours++
        }

        this.voiceTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds
          .toString()
          .padStart(2, '0')}`
      }, 1000)
      if (av === 'video') {
        //这里控制视频窗口显示
        this.$refs['videoAndShare'].isVideo = true
      }
    },
    _OnCallEnd(
      callID,
      serialID,
      serviceDirect,
      userNo,
      bgnTime,
      endTime,
      agentAlertTime,
      userAlertTime,
      fileName,
      directory,
      disconnectType,
      userParam,
      taskID,
      serverName,
      networkInfo
    ) {
      console.log('通话结束')
      this.telStatus = false
      this.dialogVisible5 = false
      this.dialogVisible6 = false
      clearInterval(this.callTimer)
    },
    _OnReportBtnStatus(btnIDS) {
      console.log(btnIDS)
      var arrIDS = btnIDS.split('|')
      // for (var i = 0; i < arrIDS.length; i++) {
      //   this['btn' + parseInt(arrIDS[i])] = false
      // }
    },
    _OnCallRing(
      callingNo,
      calledNo,
      orgCalledNo,
      callData,
      serialID,
      serviceDirect,
      callID,
      userParam,
      taskID,
      userDn,
      agentDn,
      areaCode,
      fileName,
      networkInfo,
      queueTime,
      opAgentID,
      ringTime,
      projectID,
      accessCode,
      taskName,
      cityName,
      userType,
      lastServiceId,
      lastServiceName,
      accessNumber
    ) {},
    _OnBarExit(code, description) {
      this.signIn = false
      console.log('坐席迁出')
    },
    _OnUpdateVideoWindow(param) {
      console.log('OnUpdateVideoWindow-----', param)
      if (param.key_word == 'GetVideoViews') {
        param.param.SetVideoViews('selfView_cincc_5g', 'remoteView_cincc_5g')
      }
    },
    _OnAgentWorkReport(workStatus, description) {},
    handleSignIn() {
      if (this.signIn) {
        this.VccBar.UnInitial()
      } else {
        // 如果已经加载过cmscVccBar，不再重新加载
        if (!this.load) {
          this.VccBar = VccBar.setConfig(this.config).client()
          this.VccBar.load('cmscVccBar').then(() => {
            this.load = true
            this.VccBar.Initial()
          })
        } else {
          this.VccBar.Initial()
        }
      }
    },
    openCall(phone) {
      this.dialogVisible5 = true
      this.phoneCode = phone
      this.muteFlag = false
      this.telStatus = false
      this.VccBar.MakeCall('15757903005', 3, '', '', '', '', '', '', '', '', 1)
      this.voiceTime = '00:00:00'
    },
    openVideo(phone) {
      this.dialogVisible6 = true
      this.phoneCode = phone
      this.telStatus = false
      this.VccBar.MakeCall('15757903005', 3, '', '', '', '', '', '', '', '', 2)
      this.voiceTime = '00:00:00'
    },
    changeMute(mute) {
      this.muteFlag = !mute
      this.VccBar.Mute(this.muteFlag ? 2 : 1)
    },
    endCall() {
      this.VccBar.Disconnect()
      clearInterval(this.callTimer)
      this.dialogVisible5 = false
      this.dialogVisible6 = false
    },
    //网络通话结束
    initApi(city) {
      this.initZhtx(city)
      if (city == '金华市') {
        this.tab = ['市直部门', '县市区指挥中心']
      } else {
        this.tab = ['县市区指挥中心', '乡镇街道指挥中心']
      }
      this.activeItem = this.tab[0]
    },
    //初始化指挥体系
    initZhtx(city) {
      this.getSzbm(city)
      this.getXsqzhzx(city)
      this.getXzjdzhzx(city)
    },
    //市直部门
    getSzbm(city) {
      getZhtxList({ area: city, type: '1' }).then((res) => {
        if (res.code == 200) {
          this.szbmTable = res.data.map((item) => {
            return {
              bm: item.deptName,
              name: item.name,
              zw: item.duties,
              phone: item.phone,
            }
          })
        }
      })
    },
    //县市区指挥中心
    getXsqzhzx(city) {
      getZhtxList({ area: city, type: '2' }).then((res) => {
        if (res.code == 200) {
          this.xsqTableData = res.data.map((item) => {
            return {
              bm: item.deptName,
              name: item.name,
              zw: item.duties,
              phone: item.phone,
            }
          })
        }
      })
    },
    //乡镇街道指挥中心
    getXzjdzhzx(city) {
      getZhtxList({ area: city, type: '3' }).then((res) => {
        if (res.code == 200) {
          this.xzjdTableData = res.data.map((item) => {
            return {
              bm: item.town,
              name: item.name,
              zw: item.duties,
              phone: item.phone,
            }
          })
        }
      })
    },
    openManage() {
      window.open(this.manageUrl)
    },
    changeTab(item, index) {
      this.activeIndex = index
      this.activeItem = item
    },
    toPhone(phone) {
      var reg = /(\d{3})\d{4}(\d{4})/ //正则表达式
      return phone.replace(reg, '$1****$2')
    },
    mouseenterEvent2() {
      clearInterval(this.time2)
    },
    mouseleaveEvent2() {
      this.time2 = setInterval(() => {
        // this.dom1.scrollTop += 1.5
        this.dom2.scrollBy({
          top: 93,
          behavior: 'smooth',
        })
        if (this.dom2.scrollTop >= this.dom2.scrollHeight - this.dom2.offsetHeight) {
          this.dom2.scrollTop = 0
        }
      }, 1500)
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.zhddTitleBar {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.Phoneicon {
  width: 32px;
  height: 32px;
  background: url("@/assets/images/phone-icon.png") no-repeat;
  background-size: contain;
}
.Videoicon {
  width: 32px;
  height: 32px;
  background: url("@/assets/images/video-icon.png") no-repeat;
  background-size: contain;
}
.tabChange {
  display: flex;
  font-size: 30px;
  position: relative;
  top: 0;
  left: 0;
  color: #fff;
  justify-content: flex-start;
  align-items: center;
}
.tabItem {
  height: 59px;
  line-height: 59px;
  cursor: pointer;
  margin-left: 40px;
  padding-bottom: 5px;
  border-bottom: 4px solid transparent;
  box-sizing: border-box;
  white-space: nowrap;
}
.tabActive {
  width: 236px;
  height: 59px;
  color: #FFFFFF;
  background: url("@/assets/common/activeBg.png") no-repeat;
  background-size: 100% 100%;
  text-align: center;
  line-height: 59px;
}
.zhtx {
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;
  height: 505px;
  overflow: hidden;
}
/* 表格 */
.table {
  width: 100%;
  height: 100%;
  padding: 10px;
  box-sizing: border-box;
}

.table .th {
  width: 100%;
  height: 92px;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  font-weight: 700;
  font-size: 28px;
  line-height: 60px;
  color: #DCEFFF;
  background: #09265A;
}

.table .th_td {
  letter-spacing: 0px;
  text-align: left;
}

.table .tbody {
  width: 100%;
  height: calc(100% - 80px);
  overflow: hidden;
}

.table .tbody:hover {
  overflow-y: auto;
}

.table .tbody::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.table .tbody::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #20aeff;
  height: 8px;
}

.table .tr {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  height: 92px;
  line-height: 88px;
  padding-top: 0px;
  font-size: 28px;
  color: #DCEFFF;
  cursor: pointer;
  border-top: 1px solid #959aa1;
  border-image: linear-gradient(to right, #e9f5ff3b, #f5ffffd4, #e9f5ff3b) 1;
  box-sizing: border-box;
}

.table .tr:nth-child(2n) {
  background: #091E45;
}

.table .tr:nth-child(2n + 1) {
  background: #0A1532;
}

.table .tr:hover {
  background-color: #0074da75;
}

.table .tr_td {
  letter-spacing: 0px;
  text-align: left;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table .tr_td > img {
  position: relative;
  top: 25px;
}

/deep/.call_dialog {
  background: transparent;
  .el-dialog__header {
    background: rgba(61, 115, 255, 0.6);
    border-radius: 0px 0px 0px 0px;
    border: 2px solid rgba(61, 115, 255, 0.72);
    border-bottom: none;
  }
  .el-dialog__title {
    font-weight: 500;
    font-size: 36px;
    color: #ffffff;
    line-height: 38px;
  }
  .el-dialog__body {
    background: rgba(16, 28, 63, 0.9);
    box-shadow: 0px 16px 32px 0px rgba(0, 0, 0, 0.5);
    border-radius: 0px 0px 0px 0px;
    border: 2px solid rgba(61, 115, 255, 0.72);
  }
}
.tel_box {
  text-align: center;
  .tel_top {
    display: flex;
    align-content: center;
    align-items: center;
    justify-content: center;
    margin-top: 66px;
    .remoteView_box {
      width: 297px;
      height: 516px;
      background: url('@/assets/ajhf/video_left.png') 0 0 no-repeat;
      background-size: cover;
      display: flex;
      align-content: center;
      align-items: center;
      justify-content: center;
      margin-right: 88px;
      .remoteView {
        width: 280px;
        height: 500px;
      }
    }

    .tel_header {
      width: 169px;
      height: 193px;
      margin-right: 88px;
    }
    .info_box {
      .tel {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 700;
        font-size: 40px;
        color: #fff;
        line-height: 48px;
        margin-bottom: 26px;
      }
      .status_box {
        display: flex;
        align-content: center;
        align-items: center;
        .status {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 32px;
          color: #2cd7aa;
          line-height: 48px;
          margin-right: 20px;
        }
        .green_status {
          color: #b2d8ff;
        }
        .status_img1 {
          width: 99px;
          height: 38px;
        }
        .status_img2 {
          width: 32px;
          height: 32px;
        }
      }
      .voice_box {
        display: flex;
        align-content: center;
        align-items: center;
        margin-top: 26px;
        .voice_img {
          width: 48px;
          height: 48px;
          margin-right: 32px;
          cursor: pointer;
        }
        .voice_time {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 32px;
          color: #fff;
          line-height: 48px;
        }
      }
      .selfView_box {
        width: 226px;
        height: 146px;
        background: url('@/assets/ajhf/video_right.png') 0 0 no-repeat;
        background-size: cover;
        display: flex;
        align-content: center;
        align-items: center;
        justify-content: center;
        margin-top: 88px;
        .selfView {
          width: 210px;
          height: 130px;
        }
      }
    }
  }
  .tel_center {
    margin-top: 64px;
    .tel_end {
      width: 96px;
      height: 96px;
      cursor: pointer;
    }
  }
}
</style>