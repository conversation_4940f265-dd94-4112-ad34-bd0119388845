import Carousel from './src/main';
import CarouselItem from './src/item';
import EnhancedCarouselItem from './src/enhanced-item';

// 增强版走马灯组件，支持1/3显示功能
const EnhancedCarousel = {
  ...Carousel,
  name: 'EnhancedCarousel'
};

// 安装函数
EnhancedCarousel.install = function(Vue) {
  Vue.component(EnhancedCarousel.name, EnhancedCarousel);
  Vue.component('EnhancedCarouselItem', EnhancedCarouselItem);
  Vue.component('ElCarousel', Carousel);
  Vue.component('ElCarouselItem', CarouselItem);
};

// 导出组件
export default EnhancedCarousel;
export { EnhancedCarouselItem, CarouselItem as ElCarouselItem };
