import {request} from '@/utils/request'

//指挥体系列表
export function getZhtxList(params) {
  return request({
    url: `/xzzfj/dutyPersonnel/dutyList`,
    method: 'get',
    params
  })
}

//多维感知视频（根据点位获取）
export function getDwgzVideoByPoint(params) {
  return request({
    url: `/jhyjzh/home/<USER>
    method: 'get',
    params
  })
}

//多维感知视频（根据id获取）
export function getDwgzVideoById(params) {
  return request({
    url: `/jhyjzh-server/screen_api/zhddzx/evetVideo`,
    method: 'get',
    params
  })
}

//舆情上报
export function yqReport(data) {
  return request({
    url: `/jhyjzh-server/screen_api/xzzfyq_state_update`,
    method: 'post',
    data
  })
}

//舆情详情
export function getYqDetail(params) {
  return request({
    url: `/screen/yq/yqInfo`,
    method: 'get',
    params
  })
}

//接收告警
export function getWarning(params) {
  return request({
    url: `/jhyjzh-server/screen_api/zhdd/xzzf/updateAlarmStatus`,
    method: 'get',
    params
  })
}

//获取后管token
export function getAdminToken(params) {
  return request({
    url: `/token/InfoAccount`,
    method: 'get',
    params
  })
}

//获取详情
export function getNoticeDetail(params) {
  return request({
    url: `/xzzfj/workNotice/getWorkNoticeDetailsNew`,
    method: 'get',
    params
  })
}

//获取部门列表
export function getDepartMentList(params) {
  return request({
    url: `/xzzfj/workNotice/getXzzfList`,
    method: 'get',
    params
  })
}

//获取所属部门
export function getDepart(params) {
  return request({
    url: `/system/dept/getUserDept`,
    method: 'get',
    params
  })
}

//指令下达
export function WorkNotifi(data) {
  return request({
    url: `/xzzfj/workNotice/xzzfjWorkNotificationArea`,
    method: 'post',
    data
  })
}

export function xzzfjWorkNotificationArea(data) {
  return request({
    url: `/xzzfj/workNotice/xzzfjWorkNotificationArea`,
    method: 'post',
    data
  })
}