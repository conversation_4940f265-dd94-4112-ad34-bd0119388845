var cBar = (function () {
    return {
        /**
         * 加载引用的js
         * @returns {Promise}
         */
        load: function (path) {
            console.log('init');
            let jsList = [
                'vccbar/c-js/jssip.min.js',
                'vccbar/c-js/jcinvccbar-pure.min.js'
            ];
            return VccBar.loadScriptsSequentially(jsList, path)
        },
        _init: function () {
            var defaultBtn = '0,1,2,3,4,5,6,7,8,9,10,11,12,13,15,16,19,20,21,22,23,24,25,26,27,30';
            var hiddenBtn = '19,20,21,22,30';
            application.DislayLog(1 | 8 | 16, 1);
            application.oJVccBar.SetAttribute('MediaFlag', VccBar.config.vccId);//500500
            application.oJVccBar.SetAttribute('MainIP', VccBar.config.ip);  //**************
            application.oJVccBar.SetAttribute('MainPortID', parseInt(VccBar.config.port));  //5049
            application.oJVccBar.SetAttribute('SipServerIP', VccBar.config.ip);
            application.oJVccBar.SetAttribute('SipServerPort', parseInt(VccBar.config.port));
            application.oJVccBar.SetAttribute('SipProtocol', 'wss');
            application.oJVccBar.SetAttribute('SipPassWord', VccBar.config.password);
            application.oJVccBar.SetAttribute('AppType', 0);
            application.oJVccBar.SetAttribute('PassWord', VccBar.config.password);
            application.oJVccBar.SetAttribute('AgentID', '000010' + VccBar.config.vccId + VccBar.config.agentId);//8428
            application.oJVccBar.SetAttribute('Dn', '000002' + VccBar.config.vccId + VccBar.config.agentId);
            application.oJVccBar.SetAttribute('SipXPath', 'sbc');
            application.oJVccBar.SetAttribute('ProtocolType', 1);
            application.oJVccBar.SetAttribute('PhonType', 1);
            application.oJVccBar.SetAttribute('ForeCastCallAutoAnswer', parseInt(VccBar.config.foreCastCallAutoAnswer ? VccBar.config.foreCastCallAutoAnswer : 0))
            application.oJVccBar.SerialBtn(defaultBtn, hiddenBtn);
            application.oJVccBar.Initial();
        },
        /**
         * 初始化，设置完座席属性后，调用该方法并启动电话终端，并连接CTI服务器。
         */
        Initial: function () {
            if (application && application.oJVccBar) {
                console.log(application.oJVccBar);
                this._init();
            } else {
                this.createVccBar(() => {
                    console.log(application.oJVccBar);
                    this._init();
                });
            }
        },
        /**
         *释放电话条，关闭电话终端，断开和CTI之间的连接
         */
        UnInitial: function () {
            application.oJVccBar.UnInitial(0);
        },
        /**
         * 置闲
         */
        SetIdle: function () {
            application.oJVccBar.SetIdle();
        },
        /**
         * 置忙
         * @param subStatus 子状态 默认0
         */
        SetBusy: function (subStatus) {
            application.oJVccBar.SetBusy(subStatus ? subStatus : 0);
        },
        /**
         *外呼
         * @param destNum 目标号码或者人工服务长号
         * @param serviceDirect 可选参数  3：人工外呼，坐席在话条上点击呼出的按钮，先呼坐席，再呼用户，无外呼任务
         * @param taskId   可选参数 服务号，string
         * @param transParentParam 可选参数，透明参数，string 长度不超过255字节
         * @param phoneID 可选参数，号码编号，string
         * @param projectID 可选参数，号码编号，string
         * @param cryptType 可选参数，DestNum是否加密，0不加密1：DES加密
         * @param chargeNumber 可选参数，计费分机号，string
         * @param displayNumber 可选参数，显示号码，string
         * @param numberX 可选参数，中间号码，string
         * @param av 可选参数  1音频, 2视频
         */
        MakeCall: function (destNum, serviceDirect, taskId, transParentParam, phoneID, projectID, cryptType, chargeNumber, displayNumber, numberX, av) {
            application.oJVccBar.MakeCall(destNum, serviceDirect, '', '', '', '', '', '', displayNumber, '', av);
        },
        /**
         *内呼
         * @param destAgentID 目标座席工号（长号、短号都行）
         * @param serviceDirect 5：内部呼叫坐席在话条上呼叫另外一个坐席
         * @param taskID 可选参数，服务号，string
         * @param transParentParam 可选参数，透明参数，string
         * @param av 可选参数  1音频, 2视频
         */
        CallIn: function (destAgentID, serviceDirect, taskID, transParentParam, av) {
            application.oJVccBar.CallIn(destAgentID, serviceDirect, taskID, '', av);
        },
        /**
         * 保持
         */
        Hold: function () {
            application.oJVccBar.Hold();
        },

        /**
         * 接回
         */
        RetrieveHold: function () {
            application.oJVccBar.RetrieveHold();
        },
        /**
         * 二次拨号
         * @param sKey 二次拨号号码，数字和#、*组成字符串
         */
        SendDTMF: function (sKey) {
            application.oJVccBar.SendDTMF(sKey);
        },

        /**
         * 挂断
         * @param callType 可选参数 挂断呼叫类型：0：挂断呼叫方，1：挂断整个呼叫 默认 0
         */
        Disconnect: function (callType) {
            if (!callType) {
                callType = 0;
            }
            application.oJVccBar.Disconnect(callType);
        },

        /**
         * 转移
         */
        Transfer: function () {
            application.oJVccBar.Transfer();
        },

        /**
         * 会议
         */
        Conference: function () {
            application.oJVccBar.Conference();
        },

        /**
         * 应答
         */
        Answer: function () {
            application.oJVccBar.Answer(0);
        },

        /**
         * 转出
         * @param transferType 转移类型：0:座席工号，1：外部号码，2：服务号
         * @param destNum 目标号码
         */
        TransferOut: function (transferType, destNum) {
            application.oJVccBar.TransferOut(transferType, destNum);
        },
        /**
         * 咨询
         * @param lConsultType 咨询类型：0：座席工号，1：外部号码，2：人工服务
         * @param destNum 目标号码
         */
        Consult: function (lConsultType, destNum) {
            application.oJVccBar.Consult(lConsultType, destNum);
        },
        /**
         * 静音
         * @param flag 1：静音设置 0：静音取消
         */
        Mute: function (flag) {
            application.oJVccBar.Mute(flag);
        },
        /**
         * 得到座席状态
         * @returns {0|1|2|3|4}
         */
        GetAgentStatus: function () {
            return application.oJVccBar.GetAgentStatus();
        },
        /**
         * 切换音视频
         * @param distnum
         * @param type
         * @constructor
         */
        UpdateMedia: function(distnum,type){
            application.oJVccBar.UpdateMedia(distnum,type)
        },
        createVccBar: function (c) {
            applicationLoad('', function () {
                console.log(application.oJVccBar);
                application.oJVccBar.OnMethodResponseEvent = VccBar.OnMethodResponseEvent;
                application.oJVccBar.On({
                    OnCallRing: VccBar.OnCallRing,
                    OnAnswerCall: VccBar.OnAnswerCall,
                    OnCallEnd: VccBar.OnCallEnd,
                    OnPrompt: VccBar.OnPrompt,
                    OnReportBtnStatus: VccBar.OnReportBtnStatus,
                    OnInitalSuccess: VccBar.OnInitalSuccess,
                    OnInitalFailure: VccBar.OnInitalFailure,
                    OnAgentWorkReport: VccBar.OnAgentWorkReport,
                    OnBarExit: VccBar.OnBarExit,
                    OnUpdateVideoWindow: VccBar.OnUpdateVideoWindow,
                    OnUpdateMediaEvent: VccBar.OnUpdateMediaEvent,
                });
                c();
            });
        }
    };
})();


var yBar = (function () {
    var Btns = [
        {name: 'agentnotready', value: '1', des: '示忙'},
        {name: 'agentready', value: '2', des: '示闲'},
        {name: 'makecall', value: '3', des: '呼出'},
        {name: 'holdcall', value: '4', des: '保持'},
        {name: 'unholdcall', value: '5', des: '接回'},
        {name: 'clearcall', value: '6', des: '挂断'},
        {name: 'transfercall', value: '7', des: '转移'},
        {name: 'conferencecall', value: '8', des: '会议'},
        {name: 'answercall', value: '9', des: '应答'},
        {name: 'sstransfer', value: '10', des: '转出'},
        {name: 'consultationcall', value: '11', des: '咨询'},
        {name: 'dtmf', value: '12', des: '二次拨号'},
        {name: 'mutecall', value: '27', des: '静音'}
    ];
    return {
        load: function (path) {
            let jsList = [
                'vccbar/y-js/ccbar.js',
                'vccbar/y-js/md5.js',
                'vccbar/y-js/ccbar_sdk.js',
                'vccbar/y-js/tripledes.js',
                'vccbar/y-js/mode-ecb.js',
                'vccbar/y-js/yqwebrtc-sdk-3.0.js'
            ];
            return VccBar.loadScriptsSequentially(jsList, path)
        },
        _init: function () {
            let ccbarOptions = {
                debug: false, //是否输出日志
                url: `https://${VccBar.config.ip}:${VccBar.config.port}`, //服务器地址
                entId: VccBar.config.vccId, //企业id
                productId: '003', //产品id
                loginKey: VccBar.config.loginKey, //秘钥
                agentId: '000010' + VccBar.config.vccId + VccBar.config.agentId, //坐席工号
                password: VccBar.config.password, //密码
                token: hex_md5(VccBar.config.password), //md5加密的密码,优先级比password高
                dn: '115010' + VccBar.config.vccId + VccBar.config.agentId, //话机
                readyMode: 'ready', //签入后忙还是闲 ready / notReady
                workMode: 'All', //工作模式,只接电话;接听、外呼电话
                autoAnswer: VccBar.config.foreCastCallAutoAnswer == 1 ? true : false, //自动应答
                autoLogin: true,//自动签入
                event: {//事件回调,修改对应回调方法即可
                    logon: this.adapter.callback_logon.bind(this.adapter),//签入
                    logoff: this.adapter.callback_logoff.bind(this.adapter),//签出
                    agentStateSync: this.adapter.callback_agentStateSync.bind(this.adapter),//坐席状态变更
                    evtAltering: this.adapter.callback_evtAltering.bind(this.adapter),//振铃
                    evtConnected: this.adapter.callback_evtConnected.bind(this.adapter),//接通
                    evtDisConnected: this.adapter.callback_evtDisConnected.bind(this.adapter),//挂断,
                    webrtcVideoChatConnect: this.adapter.videoChatConnect.bind(this.adapter),//Webrtc 话机视频通话接通推流事件
                    requestNotify: this.adapter.requestNotify.bind(this.adapter),
                    webrtcCallTypeChange: this.adapter.webrtcCallTypeChange.bind(this.adapter),
                },
                layout: false, //false的时候不使用默认话务条
                background: null,
                webrtcPhone: false,
                reset: 1,//强制签入
                webrtc: {log: false},//关闭webrtc打印的日志
                websocket: true,//使用websocket推送
                agentType: 7,
                alteringRing: true,
                disconnectRing: false,
                stateBan: {//业务定制,在指定状态禁用功能
                    'TALK': ['agentnotready', 'agentready'],//通话中设置置忙置闲不可用
                    'CONSULTED': ['agentnotready', 'agentready'],//咨询中设置置忙置闲不可用
                    'CONFERENCED': ['agentnotready', 'agentready']//三方中设置置忙置闲不可用
                }
            };
            CallControl.init(ccbarOptions);
        },
        Initial: function () {
            this._init();
        },
        UnInitial: function () {
            CallControl.logoff((res) => {
                this.adapter.log('UnInitial', res);
                VccBar.OnMethodResponseEvent('UnInitial', res)
            });
        },
        /**
         * 设置示闲
         */
        SetIdle: function () {
            CallControl.agentReady(res => {
                this.adapter.log('[SetIdle]', res);
                VccBar.OnMethodResponseEvent('SetIdle', res)
            });
        },
        /**
         * 设置示忙
         * @param subStatus
         */
        SetBusy: function (subStatus) {
            CallControl.agentNotReady('', res => {
                this.adapter.log('[setBusy]', res);
                VccBar.OnMethodResponseEvent('SetBusy', res)
            });
        },
        /**
         * 外呼
         * @param destNum
         * @param serviceDirect
         * @param displayNumber
         * @param av
         */
        MakeCall: function (destNum, serviceDirect, taskId, transParentParam, phoneID, projectID, cryptType, chargeNumber, displayNumber, numberX, av) {
            var userData = {workReadyFlag: '1', workReadyTimeout: '0'};
            if (transParentParam) {
                userData = {workReadyFlag: '1', workReadyTimeout: '0', ...transParentParam}
            }
            if (av == 1) {
                CallControl.makeCall(destNum, displayNumber, userData, res => {
                    this.adapter.log('[makeCall]', res);
                    VccBar.OnMethodResponseEvent('MakeCall', res)
                }, 2);
            } else {
                CallControl.videoMakeCall(destNum, displayNumber, userData, res => {
                    this.adapter.log('[videoMakeCall]', res);
                    VccBar.OnMethodResponseEvent('MakeCall', res)
                }, 2);
            }
        },
        /**
         * 内呼
         * @param destNum
         * @param displayNumber
         * @param av
         */
        CallIn: function (destAgentID, serviceDirect, taskID, transParentParam, callBack) {
            let userData = {workReadyFlag: '1', workReadyTimeout: '0'};
            if (av == 1) {
                CallControl.makeCall(destAgentID, displayNumber, userData, res => {
                    this.adapter.log('[callIn]', res);
                    VccBar.OnMethodResponseEvent('CallIn', res)
                }, 1);
            } else {
                CallControl.videoMakeCall(destAgentID, displayNumber, userData, res => {
                    this.adapter.log('[callIn]', res);
                    VccBar.OnMethodResponseEvent('CallIn', res)
                }, 1);
            }
        },
        /**
         * 保持
         */
        Hold: function () {
            CallControl.holdCall(res => {
                this.adapter.log('[hold]', res);
                VccBar.OnMethodResponseEvent('Hold', res)
            });
        },

        /**
         * 接回
         */
        RetrieveHold: function () {
            CallControl.unholdCall(res => {
                this.adapter.log('[retrieveHold]', res);
                VccBar.OnMethodResponseEvent('RetrieveHold', res)
            });
        },

        /**
         * 二次拨号
         * @param sKey
         */
        SendDTMF: function (sKey) {
            CallControl.webrtc.device.sendDTMF(sKey);
        },

        /**
         * 挂断
         * @param callType
         */
        Disconnect: function (callType) {
            CallControl.clearCall(res => {
                this.adapter.log('[Disconnect]', res);
                VccBar.OnMethodResponseEvent('Disconnect', res)
            });
        },

        /**
         * 转移
         */
        Transfer: function () {
            CallControl.sstransfer(res => {
                this.adapter.log('[transfer]', res);
                VccBar.OnMethodResponseEvent('Transfer', res)
            });
        },

        /**
         * 会议
         */
        Conference: function () {
            return CallControl.conference(res => {
                this.log('[conference]', res);
                VccBar.OnMethodResponseEvent('Conference', res)
            });
        },

        /**
         * 应答
         * @param recordFlag
         */
        Answer: function (recordFlag) {
            return CallControl.answerCall(res => {
                this.adapter.log('[answer]', res);
                VccBar.OnMethodResponseEvent('Answer', res)
            });
        },

        /**
         * 转出
         * @param lTransferType 0:坐席工号 1外部号码 2服务号
         * @param destNum
         */
        TransferOut: function (lTransferType, destNum) {
            let code;
            if (lTransferType == 0) {
                code = 1;
            } else if (lTransferType == 1) {
                code = 3;
            } else if (lTransferType == 2) {
                code = 4;
            } else {
                code = 2;
            }
            
            CallControl.sstransfer(destNum, '', '', code, '', res => {
                this.adapter.log('[transferOut]', res);
                VccBar.OnMethodResponseEvent('TransferOut', res)
            });
        },
        /**
         * 咨询
         * @param lConsultType
         * @param destNum
         */
        Consult: function (lConsultType, destNum) {
            let code;
            if (lConsultType == 0) {
                code = 1;
            } else if (lConsultType == 1) {
                code = 3;
            } else if (lConsultType == 2) {
                code = 4;
            } else {
                code = 2;
            }
            CallControl.consultation(destNum, '', '', code, '', res => {
                this.adapter.log('consult', res);
                VccBar.OnMethodResponseEvent('Consult', res)
            });
        },

         /**
         * 视频咨询
         * @param lConsultType
         * @param destNum
         */
         videoConsult: function (lConsultType, destNum, displayNum) {
            let code;
            if (lConsultType == 0) {
                code = 1;
            } else if (lConsultType == 1) {
                code = 3;
            } else if (lConsultType == 2) {
                code = 4;
            } else {
                code = 2;
            }
            CallControl.videoConsultation(destNum, displayNum, '',code, '', res => {
                this.adapter.log('videoConsult', res);
                VccBar.OnMethodResponseEvent('VideoConsult', res)
            });
        },

        /**
         * 桥接
         * @param ivrNum
         * @param bEndCall
         */
        Bridge: function (ivrNum, bEndCall) {
            CallControl.consultIVR(ivrNum, '', '', res => {
                this.adapter.log('bridge', res);
                VccBar.OnMethodResponseEvent('Bridge', res)
            });
        },
        /**
         * 静音
         * @param flag 1：静音设置 0：静音取消
         */
        Mute: function (flag) {
            CallControl.mute(res => {
                this.adapter.log('[mute]', res);
                VccBar.OnMethodResponseEvent('Mute', res)
            });
        },
        /**
         * 得到座席状态
         * @returns {0|1|2|3|4}
         */
        GetAgentStatus: function () {
            return this.adapter.getYqAgentState(CallControl.getState());
        },
        /**
         * 切换音视频
         * @param distnum
         * @param type
         * @constructor
         */
        UpdateMedia: function(distnum,type){
            if (type == 'video') {
                CallControl.webrtc.device.convertToVideoCall();
            } else {
                CallControl.webrtc.device.convertToAudioCall();
            }
        },
        adapter: {
            /**
             * 转换坐席状态
             */
            getYqAgentState: function (status) {
                switch (status) {
                    case 'LOGOFF':
                        return 0;
                    case 'IDLE':
                        return 2;
                    case 'BUSY':
                        return 1;
                    case 'ALERTING':
                        return 3;
                    case 'WORKNOTREADY':
                        return 4;
                    case 'TALK':
                        return 3;
                    case 'CONSULTED':
                        return 3;
                    case 'CONFERENCED':
                        return 3;
                    case 'HELD':
                        return 3;
                    case 'MONITORED':
                        return 3;
                    case 'OCCUPY':
                        return 3;
                }
            },
            /*视频升降级状态*/
            webrtcCallTypeChange(event) {
                this.log('【视频升降级】', event);
                VccBar.OnUpdateMediaEvent('', '', event.type);
            },
            //  视频通话接通推流事件
            videoChatConnect: function (e) {
                this.log('[视频通话推流事件]', e);
                if (e.msgData.list) {
                    e.msgData.list.forEach((item) => {
                        let srcObject = item.stream; //视频流
                        if (item.type == 'remote') {//type 为 remote 时表示为客户的视频流
                            this.remoteVideoStream = srcObject;
                            VccBar.OnUpdateVideoWindow({
                                key_word: 'OnGetRemoteView'
                            });
                        } else {//坐席的视频流
                            this.localVideoStream = srcObject;
                            VccBar.OnUpdateVideoWindow({
                                key_word: 'OnGetLocalView'
                            });
                        }
                    });
                    if (this.remoteVideoStream && this.localVideoStream) {
                        VccBar.OnUpdateVideoWindow({
                            key_word: 'GetVideoViews',
                            param: {
                                SetVideoViews: (id2, id1) => {
                                    let localVideo = document.createElement('video');
                                    localVideo.srcObject = this.localVideoStream;
                                    localVideo.autoplay = true;
                                    localVideo.playinline = 'playinline';
                                    localVideo.style.width = '100%';
                                    localVideo.style.height = '100%';
                                    localVideo.style.backgroundColor = 'black';
                                    localVideo.muted = true;
                                    let localTag = document.getElementById(id2);
                                    localTag.innerHTML = '';
                                    localTag.appendChild(localVideo);

                                    let remoteVideo = document.createElement('video');
                                    remoteVideo.srcObject = this.remoteVideoStream;
                                    remoteVideo.autoplay = true;
                                    remoteVideo.style.width = '100%';
                                    remoteVideo.style.height = '100%';
                                    remoteVideo.playinline = 'playinline';
                                    remoteVideo.style.objectFit = 'cover';
                                    remoteVideo.style.backgroundColor = 'black';
                                    let remoteTag = document.getElementById(id1);
                                    remoteTag.innerHTML = '';
                                    remoteTag.appendChild(remoteVideo);
                                }
                            }
                        });
                    }
                }
            },
            callback_evtAltering: function (event) {
                this.log(['callback_evtAltering'], event)
                if (this.getServiceDirect(event.event.createCause) == '13') {//预测外呼
                    VccBar.OnCallRing(event.event.called, event.event.caller,
                        event.event.origCalled, '', event.event.sid, this.getServiceDirect(event.event.createCause),
                        event.event.callId, event.userData.UserParam, event.event.taskId, event.event.custPhone, event.event.agentId, event.event.areaInfo.area,
                        '', '', '', '', '', '');
                } else {
                    VccBar.OnCallRing(event.event.caller, event.event.called,
                        event.event.origCalled, '', event.event.sid, this.getServiceDirect(event.event.createCause),
                        event.event.callId, event.userData.UserParam, event.event.taskId, event.event.custPhone, event.event.agentId, event.event.areaInfo.area,
                        '', '', '', '', '', '');
                }
                if (event.event.createCause == '6') {
                    VccBar.OnAgentWorkReport('20', '座席外呼,对方正在振铃');
                }
            },
            /*接通*/
            callback_evtConnected: function (event) {
                this.log(['callback_evtConnected'], event)
                VccBar.OnAnswerCall(event.event.custPhone, event.agentAnswerTime, event.event.sid,
                    this.getServiceDirect(event.event.createCause), event.event.callId, event.userData.UserParam, event.event.taskId, this.getAvType(event.event.callMode));
            },
            /*挂断*/
            callback_evtDisConnected: function (event) {
                this.log(['callback_evtDisConnected'], event)
                VccBar.OnCallEnd(event.event.callId, event.event.sid, this.getServiceDirect(event.event.createCause),
                    event.event.custPhone, event.event.createTime,
                    event.event.endTime, '', '', event.event.recordFileName, '', this.getDisconnectType(event.event.agentRelease),
                    event.userData, event.event.taskId, '', '');
                VccBar.OnUpdateVideoWindow({
                    key_word: 'OnLeaveSuccess'
                });
            },
            requestNotify(event) {
                this.log('【requestNotify】', event);
                if (!event) {
                    return;
                }
                if (event.data && event.data.resultText) {
                    VccBar.OnPrompt(event.data.resultCode, event.data.resultText);
                }
            },
            getDisconnectType: function (agentRelease) {
                if (agentRelease == 2) {
                    return 1;
                }
                if (agentRelease == 1) {
                    return 2;
                }
                return 0;
            },
            getAvType: function (callMode) {
                if (callMode == '1') {
                    return 'audio';
                } else if (callMode == '2') {
                    return 'video';
                } else {
                    return '';
                }
            },
            getServiceDirect: function (createCause) {
                switch (parseInt(createCause)) {
                    case 1:
                        return 0;
                    case 2:
                        return 0;
                    case 3:
                        return 0;
                    case 4:
                    case 5:
                        return 6;
                    case 6:
                        return 3;
                    case 8://预测外呼
                        return 13;
                    case 14:
                        return 6;
                    case 15:
                        return 10;
                    case 19:
                        return 11;
                    case 20:
                        return 11;
                    case 26:
                        return 9;
                    case 29:
                        return 5;
                    default:
                        return 0;
                }

            },
            callback_logon: function (event) {
                this.log(['callback_logon'], event)
                this.resultCallBack(event, () => {
                        VccBar.OnInitalSuccess();
                    },
                    (code, descr) => {
                        VccBar.OnInitalFailure(code, descr);
                    }
                );
            },
            callback_logoff: function (event) {
                this.log(['callback_logoff'], event)
                this.resultCallBack(event, () => {
                        VccBar.OnBarExit(0, '成功');
                    },
                    (code, descr) => {
                        VccBar.OnBarExit(code, descr);
                    }
                );
            },
            callback_agentStateSync: function (event) {
                this.log(['callback_agentStateSync'], event)
                this.setReportBtnStatus(event)
                this.setAgentWorkState(event.state);
            },
            setReportBtnStatus: function (event) {
                let arr = [];
                for (let key in event.funcMask) {
                    if (event.funcMask[key]) {
                        for (let i = 0; i < Btns.length; i++) {
                            if (Btns[i].name == key) {
                                arr.push(Btns[i].value);
                            }
                        }
                    }
                }
                let btnIds = arr.join('|');
                this.log(['OnReportBtnStatus'], btnIds)
                VccBar.OnReportBtnStatus(btnIds);

            },
            setAgentWorkState: function (status) {
                switch (status) {
                    case 'LOGOFF':
                        VccBar.OnAgentWorkReport('-1', '没有初始化');
                        return;
                    case 0:
                        VccBar.OnAgentWorkReport('0', '登录 CTI 成功');
                        return;
                    case 'IDLE':
                        VccBar.OnAgentWorkReport('2', '座席就绪');
                        return;
                    case 'BUSY':
                        VccBar.OnAgentWorkReport('3', '座席忙碌');
                        return;
                    case 4:
                        VccBar.OnAgentWorkReport('4', '座席外拨');
                        return;
                    case 'ALERTING':
                        VccBar.OnAgentWorkReport('5', '座席振铃');
                        return;
                    case 7:
                        VccBar.OnAgentWorkReport('7', '座席挂机处于空闲状态');
                        return;
                    case 'WORKNOTREADY':
                        VccBar.OnAgentWorkReport('8', '座席挂机处于后处理状态');
                        return;
                    case 'TALK':
                        VccBar.OnAgentWorkReport('10', '座席接通呼入电话');
                        return;
                    case 'CONSULTED':
                        VccBar.OnAgentWorkReport('12', '座席正在咨询中');
                        return;
                    case 'CONFERENCED':
                        VccBar.OnAgentWorkReport('13', '座席在会议中');
                        return;
                    case 'HELD':
                        VccBar.OnAgentWorkReport('14', '用户处于保持中');
                        return;
                    case 'MONITORED':
                        VccBar.OnAgentWorkReport('16', '座席正在监听中');
                        return;
                    case 'OCCUPY':
                        VccBar.OnAgentWorkReport('1', '预占');
                        return;
                    case 'INTERVENTED':
                        VccBar.OnAgentWorkReport('18', '座席正在强插中');
                        return;
                    case 'SECRETLYTALK':
                        VccBar.OnAgentWorkReport('100', '座席正在私语中');
                        return;
                    case 'SERVICEOFFLINE':
                        VccBar.OnAgentWorkReport('101', '网络异常');
                        return;
                    case 'NETWORKOFFLINE':
                        VccBar.OnAgentWorkReport('102', '网络中断');
                        return;
                }
            },
            resultCallBack: function (event, successCb, failedCb) {
                if (event.resultCode == '000') {
                    successCb();
                    return;
                }
                if (event.resultCode == '001') {
                    failedCb('001', '包文格式错误');
                    return;
                }
                if (event.resultCode == '002') {
                    failedCb('002', '无效的操作请求');
                    return;
                }
                if (event.resultCode == '003') {
                    failedCb('003', '请求参数错误');
                    return;
                }
                if (event.resultCode == '004') {
                    failedCb('004', '无效的业务订购');
                    return;
                }
                if (event.resultCode == '100') {
                    failedCb('100', '无效的技能组');
                    return;
                }
                if (event.resultCode == '101') {
                    failedCb('101', '无效的坐席工号');
                    return;
                }
                if (event.resultCode == '102') {
                    failedCb('102', '无效的坐席密码');
                    return;
                }
                if (event.resultCode == '103') {
                    failedCb('103', '无效的坐席状态');
                    return;
                }
                if (event.resultCode == '104') {
                    failedCb('104', '无效的呼叫状态');
                    return;
                }
                if (event.resultCode == '105') {
                    failedCb('105', '坐席工号已登陆');
                    return;
                }
                if (event.resultCode == '106') {
                    failedCb('106', '话机已被使用');
                    return;
                }
                if (event.resultCode == '107') {
                    failedCb('107', '外呼主显号码为空');
                    return;
                }
                if (event.resultCode == '108') {
                    failedCb('108', '无效的话机号码');
                    return;
                }
                if (event.resultCode == '109') {
                    failedCb('109', '未配置席间转移字冠');
                    return;
                }
                if (event.resultCode == '110') {
                    failedCb('110', '咨询失败');
                    return;
                }
                if (event.resultCode == '111') {
                    failedCb('111', '三方会议失败');
                    return;
                }
                if (event.resultCode == '112') {
                    failedCb('112', '转移失败');
                    return;
                }
                if (event.resultCode == '113') {
                    failedCb('113', '呼坐席话机失败');
                    return;
                }
                if (event.resultCode == '114') {
                    failedCb('114', '外呼失败');
                    return;
                }
                if (event.resultCode == '115') {
                    failedCb('115', '无效的企业');
                    return;
                }
                if (event.resultCode == '116') {
                    failedCb('116', '外呼时坐席已签出');
                    return;
                }
                if (event.resultCode == '117') {
                    failedCb('117', '被系统签出');
                    return;
                }
                if (event.resultCode == '403') {
                    failedCb('403', '当前坐席工号已被签入');
                    return;
                }
                if (event.resultCode == '999') {
                    failedCb('999', '未定义错误');
                    return;
                }


            },
            log(key, msg) {
                console.log('yVccBar', `[${this.getNowTime()}] ${key} ${JSON.stringify(msg)}`);
            },
            getNowTime: () => {
                const yy = new Date().getFullYear()
                const MM = (new Date().getMonth() + 1) < 10 ? '0' + (new Date().getMonth() + 1) : (new Date().getMonth() + 1)
                const dd = new Date().getDate() < 10 ? '0' + new Date().getDate() : new Date().getDate()
                const HH = new Date().getHours() < 10 ? '0' + new Date().getHours() : new Date().getHours()
                const mm = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes()
                const ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds()
                const ms = new Date().getMilliseconds() < 10 ? '0' + new Date().getMilliseconds() : new Date().getMilliseconds()
                return yy + '-' + MM + '-' + dd + ' ' + HH + ':' + mm + ':' + ss + ':' + ms
            }
        },
    };
})();


var VccBar = (function () {
    return {
        config: {
            ip: '**************',//ip
            port: '5049',//端口
            vccId: '500500',//企业id
            agentId: '8428',//短工号
            password: 'Zyzx@10086',//密码
            event: {},//事件
            loginKey: '',//密钥
            foreCastCallAutoAnswer: 0,//0：手动 1：自动
            tenantType: '0'//租户类型
        },
        client: function () {
            switch (this.config.tenantType) {
                case 0:
                    return cBar;
                case 1:
                    return yBar;
            }
        },
        setConfig: function (config) {
            this.config = config;
            return this;
        },
        getConfig: function () {
            return this.config;
        },
        loadScript: function (url, path) {
            return new Promise((resolve, reject) => {
                // 创建一个script元素
                const script = document.createElement('script');
                if (path) {
                    script.src = path + "/" + url;
                } else {
                    script.src = url;
                }
                // 成功加载时调用resolve
                script.onload = () => resolve(url);
                // 加载失败时调用reject
                script.onerror = () => reject(new Error(`Script load error for ${url}`));
                // 将script元素添加到页面中
                document.head.appendChild(script);
            });
        },
        // 顺序加载脚本
        loadScriptsSequentially: function (scripts, path) {
            return scripts.reduce((promiseChain, currentScript) => {
                return promiseChain.then(() => this.loadScript(currentScript, path));
            }, Promise.resolve());
        },
        getVersion() {
            return '1.0.0'
        },
        /**
         *
         * @param callingNo
         * @param calledNo
         * @param orgCalledNo
         * @param callData
         * @param serialID
         * @param serviceDirect
         * @param callID
         * @param userParam
         * @param taskID
         * @param userDn
         * @param agentDn
         * @param areaCode
         * @param fileName
         * @param networkInfo
         * @param queueTime
         * @param opAgentID
         * @param ringTime
         * @param projectID
         * @param accessCode
         * @param taskName
         * @param cityName
         * @param userType
         * @param lastServiceId
         * @param lastServiceName
         * @param accessNumber
         * @constructor
         */
        OnCallRing: function (callingNo, calledNo, orgCalledNo, callData, serialID, serviceDirect, callID, userParam, taskID, userDn, agentDn, areaCode, fileName, networkInfo, queueTime, opAgentID, ringTime, projectID, accessCode, taskName, cityName, userType, lastServiceId, lastServiceName, accessNumber) {
            if (!VccBar.config.event.OnCallRing) {
                return;
            }
            VccBar.config.event.OnCallRing(callingNo, calledNo, orgCalledNo, callData, serialID, serviceDirect, callID, userParam, taskID, userDn, agentDn, areaCode, fileName, networkInfo, queueTime, opAgentID, ringTime, projectID, accessCode, taskName, cityName, userType, lastServiceId, lastServiceName, accessNumber);

        },

        /**
         *
         * @param userNo
         * @param answerTime
         * @param serialID
         * @param serviceDirect
         * @param callID
         * @param userParam
         * @param taskID
         * @param av
         * @param tc
         * @param haveAsrEvent
         * @constructor
         */
        OnAnswerCall: function (userNo, answerTime, serialID, serviceDirect, callID, userParam, taskID, av, tc, haveAsrEvent) {
            if (!VccBar.config.event.OnAnswerCall) {
                return;
            }
            VccBar.config.event.OnAnswerCall(userNo, answerTime, serialID, serviceDirect, callID, userParam, taskID, av, tc, haveAsrEvent);
        },

        /**
         *
         * @param callID
         * @param serialID
         * @param serviceDirect
         * @param userNo
         * @param bgnTime
         * @param endTime
         * @param agentAlertTime
         * @param userAlertTime
         * @param fileName
         * @param directory
         * @param disconnectType
         * @param userParam
         * @param taskID
         * @param serverName
         * @param networkInfo
         * @constructor
         */
        OnCallEnd: function (callID, serialID, serviceDirect, userNo, bgnTime, endTime, agentAlertTime, userAlertTime, fileName, directory, disconnectType, userParam, taskID, serverName, networkInfo) {
            if (!VccBar.config.event.OnCallEnd) {
                return;
            }
            VccBar.config.event.OnCallEnd(callID, serialID, serviceDirect, userNo, bgnTime, endTime, agentAlertTime, userAlertTime, fileName, directory, disconnectType, userParam, taskID, serverName, networkInfo);
        },
        /**
         *
         * @param workStatus
         * @param description
         * @constructor
         */
        OnAgentWorkReport: function (workStatus, description) {
            if (VccBar.config.event.OnAgentWorkReport) {
                VccBar.config.event.OnAgentWorkReport(workStatus, description);
            }
        },

        /**
         * 信息提示
         * @param code
         * @param description
         * @constructor
         */
        OnPrompt: function (code, description) {
            if (VccBar.config.event.OnPrompt) {
                VccBar.config.event.OnPrompt(code, description);
            }
        },

        /**
         * 按钮状态报告
         * @param btnIDS
         * @constructor
         */
        OnReportBtnStatus: function (btnIDS) {
            if (VccBar.config.event.OnReportBtnStatus) {
                VccBar.config.event.OnReportBtnStatus(btnIDS);
            }
        },

        /**
         * 初始化成功
         * @constructor
         */
        OnInitalSuccess: function () {
            if (VccBar.config.event.OnInitalSuccess) {
                VccBar.config.event.OnInitalSuccess();

            }
        },

        /**
         * 初始化失败
         * @param code
         * @param description
         * @constructor
         */
        OnInitalFailure: function (code, description) {
            if (VccBar.config.event.OnInitalFailure) {
                VccBar.config.event.OnInitalFailure(code, description);
            }
        },

        /**
         * 电话条退出事件
         * @param code
         * @param description
         * @constructor
         */
        OnBarExit: function (code, description) {
            if (VccBar.config.event.OnBarExit) {
                VccBar.config.event.OnBarExit(code, description);
            }
        },

        /**
         * 视频切换事件
         * @param param
         * @constructor
         */
        OnUpdateVideoWindow: function (param) {
            if (VccBar.config.event.OnUpdateVideoWindow) {
                VccBar.config.event.OnUpdateVideoWindow(param);
            }
        },
        /**
         * 媒体变化报告
         * @param cause
         * @param number
         * @param av
         * @constructor
         */
        OnUpdateMediaEvent: function (cause, number, av) {
            if (VccBar.config.event.OnUpdateMediaEvent) {
                VccBar.config.event.OnUpdateMediaEvent(cause, number, av);
            }
        },
        /**
         * 命令异步返回事件
         * @param cmdName
         * @param param
         * @constructor
         */
        OnMethodResponseEvent: function (cmdName, param) {
            if (VccBar.config.event.OnMethodResponseEvent) {
                VccBar.config.event.OnMethodResponseEvent(cmdName, param);
            }
        }
    };
})();
