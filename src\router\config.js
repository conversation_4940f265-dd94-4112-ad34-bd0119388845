import ScreenLayout from '@/layouts/mainLayout'
import { getToken } from '@/utils/auth'

const tabRoutes = [
  {
    path: '/home',
    name: '首页',
    component: () => import('@/pages/home/<USER>'),
    meta: {
      title: '首页',
    },
  },
  {
    path: '/dog',
    name: '犬类一张图',
    component: () => import('@/pages/dog/index'),
    meta: {
      title: '犬类一张图',
    },
  },
  {
    path: '/zqyzt',
    name: '站前一张图',
    component: () => import('@/pages/zqyzt/index'),
    meta: {
      title: '站前一张图',
    },
  },
  {
    path: '/zhdd',
    name: '指挥调度',
    component: () => import('@/pages/zhdd/index'),
    meta: {
      title: '指挥调度',
    },
  },
  {
    path: '/zfts',
    name: '执法态势',
    component: () => import('@/pages/zfts/index'),
    meta: {
      title: '执法态势',
    },
  },
  {
    path: '/ajhf',
    name: '案件回访',
    component: () => import('@/pages/ajhf/index'),
    meta: {
      title: '案件回访',
    },
  },
  {
    path: '/xjzx',
    name: '县级中心',
    component: () => import('@/pages/xjzx/index'),
    meta: {
      title: '县级中心',
    },
  },
  {
    path: '/yyjc',
    name: '应用集成',
    component: () => import('@/pages/yyjc/index'),
    meta: {
      title: '应用集成',
    },
  },
  {
    path: '/dataView',
    name: '站前一张图',
    component: () => import('@/pages/zqyztDataView/index'),
    meta: {
      title: '站前一张图',
    },
  },
  {
    path: '/monitor',
    name: '站前一张图',
    component: () => import('@/pages/zqyztMonitor/index'),
    meta: {
      title: '站前一张图',
    },
  },
  {
    path: '/csyxgl',
    name: '城市运行管理服务系统',
    component: () => import('@/pages/csyxgl/index'),
    meta: {
      title: '城市运行管理服务系统',
    },
  },
  {
    path: '/wlwsb',
    name: '物联网设备一张图',
    component: () => import('@/pages/wlwsb/index'),
    meta: {
      title: '物联网设备一张图',
    },
  },
  {
    path: '/gzfw',
    name: '公众服务一张图',
    component: () => import('@/pages/gzfw/index'),
    meta: {
      title: '公众服务一张图',
    },
  },
  {
    path: '/csgl',
    name: '城市管理一张图',
    component: () => import('@/pages/csgl/index'),
    meta: {
      title: '城市管理一张图',
    },
  },
  {
    path: '/yxjc',
    name: '运行监测一张图',
    component: () => import('@/pages/yxjc/index'),
    meta: {
      title: '运行监测一张图',
    },
  },
  {
    path: '/zhpj',
    name: '综合评价一张图',
    component: () => import('@/pages/zhpj/index'),
    meta: {
      title: '综合评价一张图',
    },
  },
]

// 在导出路由实例前添加守卫逻辑
const beforeEnter = (to, from, next) => {
  // 排除登录页本身
  if (to.path !== '/login') {
    // 检查是否存在token（假设token存储在localStorage）
    const token = getToken()
    if (!token) {
      next('/login') // 无token则跳转登录页
      return
    }
  }
  next() // 放行其他情况
}

const options = {
  routes: [
    {
      path: '/',
      redirect: '/login'  // 将根路径重定向到登录页
    },
    {
      path: '/home',
      name: '首页',
      redirect: '/home',
      component: ScreenLayout,
      children: tabRoutes,
      meta: {
        title: '首页',
      },
    },
    {
      path: '/login',
      name: '登陆页',
      component: () => import('@/pages/login/index'),
      beforeEnter, // 添加路由守卫
      meta: {
        title: '登陆页',
      },
    },
    {
      path: '/home2',
      name: '菜单页',
      component: () => import('@/pages/pageMenu/index'),
      meta: {
        title: '菜单页',
      },
    }
  ],
}
export { tabRoutes, options }
