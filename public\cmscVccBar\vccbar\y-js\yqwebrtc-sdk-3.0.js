
(function (global, factory) {
  "use strict";
  if (typeof module === "object" && typeof module.exports === "object") {
    module.exports = global.document ?
      factory(global, true) :
      function (w) {
        if (!w.document) {
          throw new Error("yqWebrtcApp requires a window with a document");
        }
        return factory(w);
      };
  } else {
    factory(global);
  }
})(typeof window !== "undefined" ? window : this, function (window, noGlobal) {
  "use strict";
  return (function (_, _C) {
    return (function (Define, _U) {
      var serverContext = null;
      var Logger = Define({
          /**
           * 日记管理器
           */
          static: {
            LOG: 0,
            INFO: 1,
            WARN: 2,
            ERROR: 3,
            enable: true
          },
          inited: function (ctx) {
            return function (config) {
              _.extend(ctx.config, config);
            }
          },
          private: {
            config: {
              log: false,
              date: true,
            },
            print: function (ctx, struc) {
              return function (type, msg) {
                if (this.config.log && struc.enable||struc.enable) {
                  var tStr = '';
                  var tDate = _.datetime();
                  var tDatetime = tDate.format() + '.' + tDate.milltime;
                  var tTypes = ['LOG', 'INFO', 'WARN', 'ERROR'];
                  var tHanler = console[tTypes[type].toLowerCase()];
                  try {
                    msg = _.toJson(msg);
                  } catch (e) {
                    msg = '{';
                    for (var key in msg) {
                      msg += key + ':' + msg[key] + ',';
                    }
                    msg = msg.replace(/,$/, '');
                    msg += '}';
                  }
                  if (this.config.date) tStr += tDatetime + ' ';
                  tStr += '<' + tTypes[type] + '> ' + msg;
                  tHanler(tStr);
                }
              }
            }
          },
          public: {
            log: function (ctx) {
              return function (msg) {
                ctx.print(Logger.LOG, msg);
              }
            },
            info: function (ctx) {
              return function (msg) {
                ctx.print(Logger.INFO, msg);
              }
            },
            warn: function (ctx) {
              return function (msg) {
                ctx.print(Logger.WARN, msg);
              }
            },
            error: function (ctx) {
              return function (msg) {
                ctx.print(Logger.ERROR, msg);
              }
            }
          }
        }),
        Timer = Define({
          /**
           * 定时器
           */
          static: {
            //当前线程，通过这个值结束线程
            currentThread: null,
            //睡眠指定时间后再执行
            sleep: function (struc) {
              return function (time, data) {
                return new Promise(function (resolve, reject) {
                  try {
                    var vTimer = new struc({
                      duration: time,
                      count: 1,
                      postData: data || {}
                    });
                    vTimer.onFinish(function (e) {
                      struc.currentThread = null;
                      resolve(e);
                    });
                    vTimer.onStop(function (e) {
                      struc.currentThread = null;
                    });
                    vTimer.start();
                    struc.currentThread = vTimer;
                  } catch (e) {
                    reject(e);
                  }
                });
              }
            },
            //启动一条线程
            thread: function (struc) {
              return function (data) {
                return new Promise(function (resolve, reject) {
                  try {
                    var vTimer = new struc({
                      duration: 0,
                      count: 1,
                      postData: data || {}
                    });
                    vTimer.onFinish(function (e) {
                      struc.currentThread = null;
                      resolve(e);
                    });
                    vTimer.onStop(function (e) {
                      struc.currentThread = null;
                    });
                    vTimer.start();
                    struc.currentThread = vTimer;
                  } catch (e) {
                    reject(e);
                  }
                })
              }
            }
          },
          inited: function (ctx) {
            //初始化
            return function (config) {
              _.extend(ctx.config, config);
              this._postData(config.postData);
            }
          },
          events: _C.TIMER_EVENT,
          private: {
            config: {
              title: '',
              duration: 1000, // 默认时长
              count: 0, //累计到该值时会停止定时器，0无限定时
              postData: {} // 传递数据
            },
            count: 0, //累计
            timeout: null, //定时器
            isPause: false, //是否暂停
            isStop: false, //是否停止
            begin: 0, //开始时间
            end: 0, //结束时间
            callback: function (ctx) {
              return function (name, code) {
                if (name) {
                  this._callback(name, _U.timer(code, {
                    count: this.count,
                    pause: this.isPause,
                    data: ctx._postData(),
                    beginTime: this.begin,
                    endTime: this.end,
                    title: this.config.title
                  }));
                }
              }
            },
            run: function (ctx) {
              return function () {
                var vThis = this;
                try {
                  if(vThis.timeout) {
                    clearTimeout(vThis.timeout);
                  }
                  vThis.timeout = setTimeout(function () {
                    if (vThis.isStop) return;
                    if (vThis.config.count != 0 && vThis.count == vThis.config.count) {
                      ctx.stop();
                      vThis.callback('finish', 10007);
                      return;
                    }
                    vThis.end = new Date().getTime();
                    vThis.count += 1;
                    vThis.callback('run', 10005);
                    vThis.run();
                  }, vThis.config.duration);
                } catch (e) {
                  vThis._callback('error', _U.timer(10006, {
                    type: e.name,
                    message: e.message
                  }))
                }
              }
            }
          },
          public: {
            start: function (ctx) {
              return function (time) {
                ctx.isStop = false;
                ctx.begin = new Date().getTime();
                if (_.isNumber(time)) ctx.config.duration = time;
                if (!this.isLive()) {
                  ctx.callback('start', 10001);
                  ctx.run();
                }
              }
            },
            stop: function (ctx) {
              return function () {
                if (this.isLive()) {
                  ctx.end = new Date().getTime();
                  clearTimeout(ctx.timeout);
                  ctx.timeout = null;
                  ctx.count = 0;
                  ctx.isPause = false;
                  ctx.callback('stop', 10002);
                }
                ctx.isStop = true;
              }
            },
            pause: function (ctx) {
              return function () {
                if (this.isLive()) {
                  ctx.isPause = true;
                  clearTimeout(ctx.timeout);
                  ctx.callback('pause', 10003);
                }
              }
            },
            resume: function (ctx) {
              return function () {
                if (this.isLive()) {
                  ctx.isPause = false;
                  ctx.run();
                  ctx.callback('resume', 10004);
                }
              }
            },
            isLive: function (ctx) {
              return function () {
                return ctx.timeout != null || ctx.isPause;
              }
            }
          }
        }),
        SenceBase = Define({
          /**
           * 场景基类，语音通话、视频通话等场景需要继承该定义
           */
          inited: function (ctx) {
            //初始化
            return function (childCtx, sence) {
              var tThis = this;
              ctx.sence = sence;
              ctx.childContext = childCtx;
              ctx.server = childCtx.server;
              ctx.logger = childCtx.server.getLogger();
              var tConfig = childCtx.server.getConfig();
              _.extend(ctx.config, {
                duration: tConfig.aliveDuration,
                log: tConfig.log
              });
              ctx.heart = new Timer({
                duration: ctx.config.duration
              });
              ctx.heart._postData({
                tip: '<' + sence + '>场景定时器'
              });
              ctx.heart
                .onStart(function (e) {
                  ctx.logger.info(e);
                  childCtx._callback('status', _U.callbackMsg(30001, sence, e.msgData));
                }).onStop(function (e) {
                  ctx.logger.info(e);
                  childCtx._callback('status', _U.callbackMsg(30002, sence, e.msgData));
                  ctx.timer.stop();
                }).onPause(function (e) {
                  ctx.logger.info(e);
                  childCtx._callback('status', _U.callbackMsg(30003, sence, e.msgData));
                }).onResume(function (e) {
                  ctx.logger.info(e);
                  childCtx._callback('status', _U.callbackMsg(30004, sence, e.msgData));
                }).onRun(function (e) {
                  childCtx._callback('status', _U.callbackMsg(30005, sence, e.msgData));
                  ctx.server.sendCommand('keep_alive', _.inArray(function (item) {
                    return item == ctx.sence;
                  }, ['videomeeting', 'audiomeeting']) ? {
                    confId: ctx.callId
                  } : {
                    callId: ctx.callId
                  });
                });
              ctx.timer = new Timer({
                duration: 1000,
                title: sence + '-计时器'
              });
              ctx.timer._postData({
                tip: '<' + sence + '>计时器'
              });
              ctx.timer.onStart(function (e) {
                childCtx._callback('timer', _U.timer(20001, e.msgData));
              }).onRun(function (e) {
                childCtx._callback('timer', _U.timer(20003, e.msgData));
              }).onStop(function (e) {
                childCtx._callback('timer', _U.timer(20002, e.msgData));
              });
              ctx.statsTimer = new Timer({
                duration: ctx.config.statsDuration,
                title: sence + '-统计器'
              });
              
            }
          },
          private: {
            config: {
              duration: 30000, //心跳时长
              statsDuration: 10000 //统计间隔
            },
            heart: null, //场景心跳
            logger: null, //日记对象
            caller: null, //主叫
            called: null, //被叫
            callId: '', //会话ID
            serialId: '', //场景创建流水号
            sence: '', //场景类型
            server: null, //服务上下文对象
            pipeMap: {},
            timer: null, //计时器
            statsTimer: null,
            isDestory: false,
            initStats: function (ctx) {
              //初始化流数据统计
              return function () {
                var vThis = this;
                var fStats = function () {
                  _.each(vThis.pipeMap, function (name, pipe) {
                    var tPeer = pipe.getConnect();
                    if (!vThis.isDestory && tPeer && pipe.isConnect()&&tPeer.getRemoteStreams) {
                      var tStreams = tPeer.getRemoteStreams();
                      if (tStreams.length > 0) {
                        tPeer.getStats(null).then(function (stats) {
                          stats.forEach(function (report) {
                            if (_.inArray(function (item) {
                                return item == report.type;
                              }, ['candidate-pair', 'inbound-rtp', 'outbound-rtp'])) {
                              vThis.childContext._callback('stats', _U.stats(10001, {
                                report: report
                              }));
                            }
                          });
                        }).catch(function (e) {
                          vThis.childContext._callback('fail', _U.stats(10002, {
                            userId: name,
                            type: e.name,
                            message: e.message
                          }));
                        })
                      }
                    } else vThis.statsTimer.stop();
                  });
                };
                vThis.statsTimer.onStart(fStats).onRun(fStats).start();
              };
            }
          },
          public: {
            getConfig: function (ctx) {
              //获取场景配置项
              return function () {
                return ctx.config;
              }
            },
            setConfig: function (ctx) {
              //设置场景配置项
              return function (config) {
                _.extend(ctx.config, config);
              }
            },
            setSerialId: function (ctx) {
              //设置场景创建流水号
              return function (serialId) {
                return ctx.serialId = serialId;
              }
            },
            getSerialId: function (ctx) {
              //获取场景创建流水号
              return function () {
                return ctx.serialId;
              }
            },
            getFromUser: function (ctx) {
              //获取目标源用户信息
              return function () {
                ctx.caller = ctx.server.getUserInfo();
                return {
                  rtcAccountID: ctx.caller.rtcAccountID,
                  userId: ctx.caller.userId
                }
              }
            },
            getToUser: function (ctx) {
              //获取目标用户信息
              return function () {
                return _.inArray(function (item) {
                  return item == ctx.sence;
                }, ['audiocall', 'audiomeeting', 'netVideoChat', 'volteCall']) ? {
                  rtcCaller: ctx.called.rtcCaller || ctx.called.rtcCalled || ctx.called.rtcAccountID
                } : {
                  rtcAccountID: ctx.called.rtcAccountID,
                  userId: ctx.called.userId
                }
              }
            },
            init: function (ctx) {
              //初始化场景
              return function (data) {
                if (data.user) ctx.called = data.user;
                ctx.callId = data.callId;
                ctx.server.bindCommand(this, {
                  'resp_keep_alive': function (e, isOk) {
                    if (!isOk) {
                      //场景心跳失败
                      //停止场景心跳
                      ctx.heart.stop();
                      //结束场景会话
                      var isMeeting = _.inArray(function (item) {
                        return item == ctx.sence;
                      }, ['videomeeting', 'audiomeeting']);
                      ctx.server.sendCommand( isMeeting? 'end' : 'hangup', isMeeting?{
                        confId: ctx.callId
                      }:{
                        callId: ctx.callId
                      });
                      ctx._callback('status', _U.callbackMsg(30007, ctx.sence, {
                        tip: e.tip
                      }));
                    } else {
                      ctx._callback('status', _U.callbackMsg(30006, ctx.sence, {
                        tip: e.tip
                      }));
                    }
                  },
                  'evt_propertie': function (e, isOk) {
                    if (isOk) {
                      ctx._callback('mediaStatus', _U.callbackMsg(20001, ctx.sence, {
                        user: e.member,
                        type: e.propertie,
                        value: e.value
                      }));
                    }
                  },
                  'evt_connect_error': function (e) {
                    if (ctx) {
                      if (ctx.timer) ctx.timer.stop();
                      if (ctx.heart) ctx.heart.stop();
                      if (ctx.childContext && ctx.childContext.peer) ctx.childContext.peer.disconnect();
                      ctx._callback('error', _U.callbackMsg(30006, ctx.sence, {
                        tip: e.tip
                      }));
                    }
                  }
                })
              }
            },
            startHeart: function (ctx) {
              //启动场景心跳
              return function () {
                ctx.heart.start();
              }
            },
            stopHeart: function (ctx) {
              //停止场景心跳
              return function () {
                ctx.heart.stop();
              }
            },
            startTimer: function (ctx) {
              //启动计时器
              return function () {
                ctx.timer.start();
                //ctx.initStats();
              }
            },
            stopTimer: function (ctx) {
              //停止计时器
              return function () {
                ctx.timer.stop();
              }
            },
            getCallId: function (ctx) {
              //获取场景会话ID
              return function () {
                return ctx.callId;
              }
            },
            getSence: function (ctx) {
              //获取会话场景类型
              return function () {
                return ctx.sence;
              }
            },
            getId: function (ctx) {
              //获取场景ID
              return function () {
                return ctx.sence + '_' + this._getId();
              }
            },
            isActive: function (ctx) {
              //场景是否活跃
              return function () {
                return ctx.heart.isLive() && ctx.callId != '';
              }
            },
            checkCallId: function (ctx) {
              //检查场景会话ID
              return function (callId) {
                return ctx.callId == callId;
              }
            },
            check: function (ctx) {
              //检查场景会话是否建立
              return function (fn) {
                if (ctx.callId) {
                  return fn.call(this, {
                    id: ctx.id,
                    sence: ctx.sence,
                    callId: ctx.callId,
                    active: this.isActive()
                  })
                } else {

                }
              }
            },
            destory: function (ctx) {
              //销毁场景会话
              return function (reason) {
                //停止场景心跳
                ctx.heart.stop();
                //停止计时器
                ctx.timer.stop();
                //停止统计
                ctx.statsTimer.stop();
                ctx.callId = '';
                ctx.called = null;
                //中断所有通道
                _.each(ctx.pipeMap, function (userId, pipe) {
                  pipe.disconnect();
                });
                ctx.pipeMap = {};
                ctx.isDestory = true;
                Device.close();
              }
            },
            createPipe: function (ctx) {
              //创建通道
              return function (userId, stream, option) {
                ctx._callback('status', _U.callbackMsg(10001, _C.PEERCONNECT, {
                  stream: stream,
                  turnServer: ctx.server.getTurnServer(),
                  optional: option,
                  userId: userId
                }));
                try {
                  if(ctx.pipeMap[userId]){
                    ctx.pipeMap[userId].disconnect();
                    ctx.pipeMap[userId] = null;
                  }
                  var tPeer = new PeerConnection({
                    turnServer: ctx.server.getTurnServer(),
                    sence: ctx.sence,
                    option: option,
                    stream: stream,
                    userId: userId,
                    log: ctx.config.log
                  });
                  ctx.pipeMap[userId] = tPeer;
                  ctx._callback('status', _U.callbackMsg(10002, _C.PEERCONNECT, {
                    pipe: tPeer
                  }));
                  return tPeer;
                } catch (e) {
                  ctx._callback('fail', _U.callbackMsg(10003, _C.PEERCONNECT, {
                    type: e.name,
                    message: e.message,
                    userId: userId
                  }));
                }
                return null;
              }
            },
            getPipe: function (ctx) {
              //获取通道
              return function (userId) {
                var tPeer = userId ? ctx.pipeMap[userId] : this.getToUser()?ctx.pipeMap[this.getToUser().rtcCaller]:null;
                if (tPeer) {
                  return tPeer.getConnect();
                } else return null;
              };
            }
          }
        }),
        VideoChat = Define({
          /**
           * 视频通话
           */
          extends: {
            name: SenceBase,
            arguments: function (ctx) {
              //向父类传递参数
              return [ctx, 'videochat']
            },
            super: function (ctx) {
              //父类执行后回调
              ctx.bindCommandEvent();
            }
          },
          inited: function (ctx) {
            //实例初始化
            return function (config, server) {
              _.extend(ctx.config, config || {});
              ctx.server = server;
              ctx.logger = server.getLogger();
            }
          },
          events: _C.VIDEOCHAT_EVENT,
          private: {
            config: {
              autoAccept: false, //自动应答
            },
            server: null, //服务上下文对象
            logger: null, //日记对象
            isRefuse: false, //是否拒接
            remoteSdp: '', //远程SDP
            called: null, //被叫信息
            peer: null, //通道对象
            waitTimer: null,
            optional: {
              optional: [{
                DtlsSrtpKeyAgreement: true
              }]
            },
            bindCommandEvent: function (ctx) {
              //绑定指令事件
              return function () {
                var vThis = this;
                vThis.server.bindCommand(ctx, {
                  'offer_one2one': function (e) {
                    if (!ctx.isActive()) {
                      vThis.logger.info('视频通话来电');
                      vThis._callback('ring', _U.videoChat(20001, {
                        user: e.from,
                        callId: e.callId
                      }));
                      vThis.isRefuse = false;
                      vThis.remoteSdp = e.data;
                      ctx.init({
                        user: e.from,
                        callId: e.callId
                      });
                      if (vThis.config.autoAccept) {
                        //自动应答
                        vThis.logger.info('已自动应答');
                        ctx.accept();
                      }
                    } else {
                      vThis.logger.warn('视频通话繁忙');
                      vThis._callback('fail', _U.videoChat(30002, {
                        callId: ctx.getCallId()
                      }));
                    }
                  },
                  'resp_offer_one2one': function (e, isOk) {
                    if (isOk) {
                      vThis._callback('status', _U.videoChat(10002, {
                        user: e.member,
                        callId: e.callId
                      }));
                      ctx.init({
                        user: vThis.called,
                        callId: e.callId
                      });
                      ctx.startHeart();
                    } else {
                      vThis.logger.warn('呼叫失败');
                      vThis._callback('fail', _U.videoChat(10003, {
                        tip: e.tip
                      }));
                    }
                  },
                  'answer_one2one': function (e, isOk) {
                    if (isOk) {
                      vThis.peer.remote(e.data);
                    }
                  },
                  'resp_answer_one2one': function (e, isOk) {
                    if (isOk) {
                      if (vThis.isRefuse) {
                        vThis.isRefuse = false;
                        vThis._callback('hangup', _U.videoChat(20004));
                        ctx.destory();
                      } else {
                        ctx.startHeart();
                      }
                    } else {
                      vThis.logger.warn('接听失败');
                      vThis._callback('fail', _U.videoChat(20003, {
                        tip: e.tip
                      }));
                    }
                  },
                  'candidate': function (e, isOk) {
                    if (isOk) {
                      vThis.peer.candidate(e.ice);
                      vThis._callback('status', _U.videoChat(20007, {
                        tip: e.tip
                      }));
                    } else {
                      vThis._callback('fail', _U.videoChat(20008, {
                        tip: e.tip
                      }));
                    }
                  },
                  'resp_candidate': function (e, isOk) {
                    if (!isOk) {
                      vThis.logger.warn('交换candidate失败');
                      vThis._callback('status', _U.videoChat(20006, {
                        tip: e.tip
                      }));
                    } else {
                      vThis._callback('fail', _U.videoChat(20005, {
                        tip: e.tip
                      }));
                    }
                  },
                  'resp_hangup': function (e, isOk) {
                    if (!isOk) {
                      vThis.logger.warn('主动挂断失败');
                      vThis._callback('fail', _U.videoChat(30004, {
                        tip: e.tip
                      }));
                    }
                  },
                  'evt_call_end': function (e, isOk) {
                    if (isOk) {
                      var tCode = 10011;
                      ctx.destory();
                      if (e.code == 'offline') {
                        tCode = 10005;
                      } else if (e.code == 'reject') {
                        tCode = 10004;
                      } else if (e.code == 'busy' || e.code == 'audioDisconnect' || e.code == 'force') {
                        tCode = 10006;
                      } else if (e.code == 'timeout') {
                        tCode = 10007;
                      } else {
                        vThis._callback('hangup', _U.videoChat(tCode, e));
                        return;
                      }
                      vThis._callback('fail', _U.videoChat(tCode, {
                        code: e.code,
                        tip: e.tip
                      }));
                    }
                  },
                  'evt_miss_call': function (e, isOk) {

                  }
                })
              }
            },
            formatReason: function (ctx) {
              //格式化事件数据
              return function (code, reason) {
                return _U.callbackMsg(code, 'VideoChat', 'videochat');
              }
            },
            initWait: function (ctx) {
              return function () {
                var tTimer = new Timer({
                  duration: 3000
                });
                tTimer._postData({
                  tip: '呼叫等待中'
                });
                tTimer.start();
                this.waitTimer = tTimer;
              }
            }
          },
          public: {
            call: function (ctx) {
              //呼叫对方
              return function (user) {
                var vThis = this;
                if(!ctx.server.isLive()){
                  ctx._callback('fail', _U.serverManager(10019));
                  return;
                }
                ctx.called = {
                  rtcAccountID: user.rtcAccountID,
                  userId: user.userId
                };
                ctx.initWait();
                ctx._callback('status', ctx.formatReason(10001));
                if (this.isActive()) {
                  ctx.logger.warn('已在通话中');
                  ctx._callback('fail', ctx.formatReason(30002));
                } else {
                  if (ctx.server.checkUserStatus(user) || !vThis.isActive()) {
                    var cons = Device.getMediaConstraint();
                    ctx._callback('status', _U.mediaDevice(10001, cons));
                    Device.open(true).then(function (stream) {
                      ctx._callback('status', _U.mediaDevice(10002));
                      ctx.peer = vThis.createPipe(ctx.called.userId, stream, ctx.optional);
                      ctx.peer.onReady(function (e) {
                        ctx._callback('status', _U.PeerConnection(10004));
                        ctx.peer.offer();
                      }).onConnect(function (e) {
                        ctx.waitTimer.stop();
                        ctx._callback('connect', _.extend(ctx.formatReason(10014), {
                          msgData: {
                            list: [{
                                type: 'local',
                                user: vThis.getFromUser(),
                                stream: stream
                              },
                              {
                                type: 'remote',
                                user: vThis.getToUser(),
                                stream: e.msgData.stream
                              }
                            ],
                            callType: Device.getMode()
                          }
                        }));
                        vThis.startTimer();
                      }).onStatus(function (e) {
                        ctx.logger.info('通道状态：' + _.toJson(e));
                      }).onFail(function (e) {
                        ctx.logger.info('通道失败：' + _.toJson(e));
                        ctx._callback('fail', ctx.formatReason(10028));
                      }).onOffer(function (e) {
                        ctx._callback('waiting', ctx.formatReason(10025));
                        var tSerialId = ctx.server.sendCommand('offer_one2one', {
                          to: ctx.called,
                          from: vThis.getFromUser(),
                          data: e.msgData.sdp
                        });
                        vThis.setSerialId(tSerialId);
                      }).onCandidate(function (e) {
                        ctx._callback('waiting', ctx.formatReason(10027));
                        ctx.server.sendCommand('candidate', {
                          ice: e.msgData.candidate,
                          callId: vThis.getCallId(),
                          to: ctx.called,
                          from: vThis.getFromUser()
                        })
                      });
                    }).catch(function (e) {
                      ctx._callback('fail', _U.mediaDevice(10003, {
                        type: e.name,
                        message: e.message
                      }))
                    })
                  } else {
                    ctx.logger.warn('对方繁忙');
                    ctx._callback('fail', ctx.formatReason(10007));
                  }
                }
              }
            },
            hangup: function (ctx) {
              //挂断通话
              return function () {
                this.check(function (e) {
                  ctx.server.sendCommand('hangup', {
                    callId: e.callId
                  })
                })
              }
            },
            accept: function (ctx) {
              //接受通话
              return function () {
                var vThis = this;
                this.check(function (e) {
                  ctx._callback('waiting', ctx.formatReason(10021));
                  Device.open().then(function (stream) {
                    ctx._callback('waiting', ctx.formatReason(10022));
                    ctx._callback('waiting', ctx.formatReason(10023));
                    ctx.peer = vThis.createPipe(vThis.getToUser().userId, stream, ctx.optional);
                    ctx.peer.onReady(function () {
                      ctx._callback('waiting', ctx.formatReason(10024));
                      ctx.peer.answer(ctx.remoteSdp);
                    }).onConnect(function (e) {
                      ctx._callback('connect', _.extend(ctx.formatReason(10014), {
                        msgData: {
                          list: [{
                              type: 'local',
                              user: vThis.getFromUser(),
                              stream: stream
                            },
                            {
                              type: 'remote',
                              user: vThis.getToUser(),
                              stream: e.msgData.stream
                            }
                          ],
                          callType: Device.getMode()
                        }
                      }));
                      vThis.startTimer();
                    }).onStatus(function (e) {
                      ctx.logger.info('通道状态：' + _.toJson(e));
                    }).onFail(function (e) {
                      ctx.logger.info('通道失败：' + _.toJson(e));
                      ctx._callback('fail', ctx.formatReason(10028));
                    }).onAnswer(function (e) {
                      ctx._callback('waiting', ctx.formatReason(10026));
                      ctx.server.sendCommand('answer_one2one', {
                        result: '000',
                        callId: vThis.getCallId(),
                        to: vThis.getToUser(),
                        from: vThis.getFromUser(),
                        data: e.msgData.sdp
                      });
                    }).onCandidate(function (e) {
                      ctx._callback('waiting', ctx.formatReason(10027));
                      ctx.server.sendCommand('candidate', {
                        ice: e.msgData.candidate,
                        callId: vThis.getCallId(),
                        to: vThis.getToUser(),
                        from: vThis.getFromUser()
                      })
                    });
                  }).catch(function (e) {
                    ctx._callback('fail', _.extend(ctx.formatReason(10001), {
                      msgData: {
                        type: e.name,
                        tip: e.message
                      }
                    }))
                  })
                })
              }
            },
            refuse: function (ctx) {
              return function () {
                this.check(function (e) {
                  ctx.isRefuse = true;
                  ctx.server.sendCommand('answer_one2one', {
                    result: -1,
                    callId: e.callId,
                    to: this.getToUser(),
                    from: this.getFromUser()
                  });
                })
              }
            }
          }
        }),
        AudioCall = Define({
          /**
           * 语音通话
           */
          extends: {
            name: SenceBase,
            arguments: function (ctx) {
              //向父类传递参数
              return [ctx, 'audiocall'];
            },
            super: function (ctx) {
              //父类执行后回调
              ctx.bindCommandEvent();
            }
          },
          inited: function (ctx) {
            //实例初始化
            return function (config, server) {
              _.extend(ctx.config, config || {});
              ctx.server = server;
              ctx.logger = server.getLogger();
            }
          },
          events: _C.AUDIOCALL_EVENT,
          private: {
            config: {
              autoAccept: false, //自动应答
              dtmfDuration: 40, //时长
              dtmfBetweenTimes: 30, //间隔时长,
              inline: true //内置播放音频
            },
            server: null, //服务上下文对象
            logger: null, //日记对象
            isRefuse: false, //是否拒接
            callout: true,
            remoteSdp: '', //远程SDP
            called: null, //被叫信息
            isCalled: false, //是否为被叫
            peer: null, //通道对象
            dtmf: null, //DTMF发送对象
            remoteAudio: null,
            localAudio: null,
            isIosAuthen: false, //ios媒体播放授权
            optional: {
              optional: [{
                DtlsSrtpKeyAgreement: true
              }]
            },
            inlinePlay: function (ctx) {
              //内置播放音频
              return function (local, remote) {
                var vThis = this;
                if (vThis.localAudio) {
                  if (!vThis.localAudio.paused) vThis.localAudio.pause();
                  vThis.localAudio = null;
                }
                if (vThis.remoteAudio) {
                  if (!vThis.remoteAudio.paused) vThis.remoteAudio.pause();
                  vThis.remoteAudio = null;
                }
                var localAudio = new Audio();
                var remoteAudio = new Audio();
                localAudio.srcObject = local;
                remoteAudio.srcObject = remote;
                localAudio.autoplay = true;
                remoteAudio.autoplay = true;
                localAudio.muted = true;
                localAudio.setAttribute('playsinline', true);
                localAudio.setAttribute('webkit-playsinline', true);
                remoteAudio.setAttribute('playsinline', true);
                remoteAudio.setAttribute('webkit-playsinline', true);
                localAudio.onpause = function () {
                  vThis.logger.info('本地音频结束播放');
                };
                localAudio.onplay = function () {
                  vThis.logger.info('本地音频开始播放');
                };
                localAudio.oncanplay = function () {
                  vThis.logger.info('本地音频就绪');
                  localAudio.play();
                };
                remoteAudio.onpause = function () {
                  vThis.logger.info('远程音频结束播放');
                };
                remoteAudio.onplay = function () {
                  vThis.logger.info('远程音频开始播放');
                };
                remoteAudio.oncanplay = function () {
                  vThis.logger.info('远程音频就绪');
                  remoteAudio.play();
                };
                if(_.isIos()) {
                  localAudio.play();
                  remoteAudio.play();
                }
                local.oninactive=function () {
                  vThis.logger.info('本地流已停止');
                  if (!localAudio.paused) localAudio.pause();
                };
                local.onactive=function(){
                  vThis.logger.info('本地流已就绪');
                  localAudio.play();
                };
                remote.oninactive=function () {
                  vThis.logger.info('远程流已停止');
                  if (!remoteAudio.paused) remoteAudio.pause();
                };
                vThis.localAudio = localAudio;
                vThis.remoteAudio = remoteAudio;
              }
            },
            bindCommandEvent: function (ctx) {
              //绑定指令事件
              return function () {
                var vThis = this;
                vThis.server.bindCommand(ctx, {
                  'offer_petra_audio': function (e) {
                    vThis.remoteSdp = e.data;
                    vThis.called = {
                      rtcAccountID: e.from.rtcCaller,
                      userId: e.from.rtcCaller
                    };
                    vThis.callout = false;
                    vThis.isCalled = true;
                    ctx.init({
                      user: vThis.called,
                      callId: e.callId
                    });
                    if (!ctx.isActive()) {
                      vThis.isRefuse = false;
                      vThis.optional.optional[0].DtlsSrtpKeyAgreement = /a=fingerprint/.test(e.data.sdp);
                      vThis.logger.info('Volte通话来电');
                      vThis._callback('ring', _.extend(vThis.formatReason(10013), {
                        msgData: {
                          user: e.from,
                          callId: e.callId
                        }
                      }));
                      if (vThis.config.autoAccept) {
                        //自动应答
                        vThis.logger.info('已自动应答');
                        ctx.accept();
                      }
                    } else {
                      vThis.connect(e.reconnect, e.reinvite, false);
                    }
                  },
                  'resp_offer_petra_audio': function (e, isOk) {
                    if (isOk) {
                      ctx.init({
                        user: vThis.called,
                        callId: e.callId
                      });
                      ctx.startHeart();
                    } else {
                      vThis.logger.warn('语音通话呼叫失败');
                      vThis._callback('fail', _.extend(vThis.formatReason(10001), {
                        msgData: e
                      }));
                      ctx.stopHeart();
                    }
                  },
                  'answer_petra_audio': function (e, isOk) {
                    if(isOk&&ctx.isActive()&&ctx.checkCallId(e.callId)){
                      vThis.peer.remote(e.data);
                    }
                  },
                  'resp_answer_petra_audio': function (e, isOk) {
                    if (isOk) {
                      if (vThis.isRefuse) {
                        vThis.isRefuse = false;
                        vThis._callback('hangup', _.extend(vThis.formatReason(10011), {
                          msgData: e
                        }));
                        ctx.destory();
                      } else {
                        ctx.startHeart();
                      }
                    } else {
                      vThis.logger.warn('语音通话响应失败');
                      vThis._callback('fail', _.extend(vThis.formatReason(10002), {
                        msgData: e
                      }));
                      ctx.destory();
                    }
                  },
                  'resp_candidate': function (e, isOk) {
                    if (!isOk) {
                      vThis.logger.warn('交换candidate失败');
                      vThis._callback('fail', _.extend(vThis.formatReason(10004), {
                        msgData: e
                      }));
                    }
                  },
                  'resp_hangup': function (e, isOk) {
                    if (!isOk) {
                      vThis.logger.warn('主动挂断失败');
                      vThis._callback('fail', _.extend(vThis.formatReason(10003), {
                        msgData: e
                      }))
                    }
                  },
                  'resp_called_reconnect_petra_audio': function (e, isOk) {
                    if (!isOk) {
                      vThis._callback('hangup', _U.audioCall(10016, e));
                      ctx.destory();
                    }
                  },
                  'evt_call_end': function (e, isOk) {
                    if (isOk) {
                      var tCode = 10011;
                      vThis.isIosAuthen = false;
                      if (e.code == 'offline') {
                        tCode = 10005;
                      } else if (e.code == 'reject') {
                        tCode = 10006;
                      } else if (e.code == 'busy') {
                        tCode = 10007;
                      } else if (e.code == 'audioDisconnect') {
                        tCode = 10008;
                      } else if (e.code == 'timeout') {
                        tCode = 10009;
                      } else if (e.code == 'force') {
                        tCode = 10010;
                      } else if (e.code == 'hangup') {
                        tCode = 10011;
                      }
                      vThis._callback('hangup', _U.audioCall(tCode, e));
                      ctx.destory();
                    }
                  },
                  'evt_miss_call': function (e, isOk) {

                  },
                  'evt_reconnect': function (e) {
                    ctx.check(function (e) {
                      if (!ctx.isActive()) {
                        //未接通时不重连
                        return;
                      }
                      vThis.logger.warn('重连通话');
                      if (vThis.isCalled) {
                        //被叫时
                        vThis.server.sendCommand('called_reconnect_petra_audio', {
                          callId: e.callId,
                          to: {
                            rtcCaller: vThis.called.rtcAccountID
                          },
                          from: ctx.getFromUser()
                        })
                      } else {
                        vThis.connect(true, false, true);
                      }
                    })
                  }
                })
              }
            },
            formatReason: function (ctx) {
              //格式化事件数据
              return function (code, reason) {
                return _U.callbackMsg(code, 'AudioCall', 'audiocall');
              }
            },
            initWait: function (ctx) {
              return function () {
                var tTimer = new Timer({
                  duration: 3000
                });
                tTimer._postData({
                  tip: '呼叫等待中'
                });
                tTimer.start();
                this.waitTimer = tTimer;
              }
            },
            initDTMF: function (ctx) {
              //初始化DTMF
              return function () {
                var tPeer = this.peer.getConnect();
                if (tPeer.getSenders) {
                  var dtmf = tPeer.getSenders().find(function(sender){
                    return sender.track.kind=='audio'&&sender.dtmf;
                  });
                  if(dtmf) this.dtmf = dtmf.dtmf;
                }
                console.log('DTMF', this.dtmf);
                _.bindEvent(this.dtmf, 'tonechange', function (e) {
                  console.log('DTMF', e);
                });
              }
            },
            connect: function(ctx){
              return function(isReconnet, isReinvite, isOffer){
                var vThis = this;
                var init = function(stream){
                  vThis._callback('status', _U.mediaDevice(10002));
                  vThis.peer = ctx.createPipe(vThis.called.rtcAccountID, stream, vThis.optional);
                  vThis.peer.onReady(function (e) {
                    vThis._callback('status', vThis.formatReason(10004));
                    if(isOffer) vThis.peer.offer();
                    else vThis.peer.answer(vThis.remoteSdp);
                  }).onConnect(function (e) {
                    vThis.initDTMF();
                    if(vThis.waitTimer) vThis.waitTimer.stop();
                    vThis._callback('connect', _.extend(vThis.formatReason(10014), {
                      msgData: {
                        list: [{
                            type: 'local',
                            user: ctx.getFromUser(),
                            stream: stream
                          },
                          {
                            type: 'remote',
                            user: ctx.getToUser(),
                            stream: e.msgData.stream
                          }
                        ]
                      }
                    }));
                    if(!isReconnet) ctx.startTimer();
                    if (vThis.config.inline) {
                      //内置播放
                      vThis.inlinePlay(stream, e.msgData.stream);
                    }
                  }).onStatus(function (e) {
                    vThis.logger.info('通道状态：' + _.toJson(e));
                  }).onFail(function (e) {
                    vThis.logger.info('通道失败：' + _.toJson(e));
                    vThis._callback('fail', vThis.formatReason(10028));
                    ctx.hangup();
                  }).onOffer(function (e) {
                    var tSerialId = '';
                    var command = 'offer_petra_audio';
                    var data = {
                      to: {
                        rtcCalled: vThis.called.rtcAccountID
                      },
                      from: ctx.getFromUser(),
                      data: e.msgData.sdp
                    };
                    if(isReconnet){
                      data.reconnect = isReconnet;
                      data.callId = ctx.getCallId();
                    }else{
                      vThis._callback('waiting', vThis.formatReason(10025));
                    }
                    tSerialId = vThis.server.sendCommand(command, data);
                    ctx.setSerialId(tSerialId);
                  }).onAnswer(function (e) {
                    var data = {
                      result: '000',
                      callId: ctx.getCallId(),
                      to: ctx.getToUser(),
                      reinvite: isReinvite,
                      from: ctx.getFromUser(),
                      data: e.msgData.sdp
                    };
                    if(isReinvite) data.reinvite = isReinvite;
                    vThis.server.sendCommand('answer_petra_audio', data);
                  });
                };
                if(ctx.isActive()&&!isReconnet){
                  vThis.logger.warn('语音通话繁忙');
                  vThis._callback('fail', _U.audioCall(30002, {
                    callId: ctx.getCallId()
                  }));
                }else if(isReconnet) {
                  vThis.logger.info('旧通道状态');
                  console.log(vThis.peer.getConnect().connectionState);
                  if (vThis.peer.isConnect()) {
                    vThis.logger.info('主动断开旧通道');
                    vThis.peer.disconnect(false);
                  } else vThis.logger.info('旧通道已断开');
                  if(ctx.isActive()){
                    init(Device.getStream());
                  }else {
                    Device.open(false, true).then(function(stream){
                      init(stream);
                    }).catch(function (e) {
                      vThis._callback('fail', _U.mediaDevice(10003, {
                        type: e.name,
                        message: e.message
                      }))
                    });
                  }
                }else {
                  var cons = Device.getMediaConstraint();
                  vThis._callback('status', _U.mediaDevice(10001, cons));
                  Device.open(false, true).then(function(stream){
                    vThis._callback('waiting', vThis.formatReason(10022));
                    vThis._callback('waiting', vThis.formatReason(10023));
                    init(stream);
                  }).catch(function (e) {
                    vThis._callback('fail', _U.mediaDevice(10003, {
                      type: e.name,
                      message: e.message
                    }));
                  });
                } 
              } 
            }
          },
          public: {
            call: function (ctx) {
              //呼叫对方
              return function (phone) {
                var vThis = this;
                if(!ctx.server.isLive()){
                  ctx._callback('fail', _U.serverManager(10019));
                  return;
                }
                ctx.initWait();
                ctx._callback('status', ctx.formatReason(10001));
                if (this.isActive()) {
                  ctx.logger.warn('已在通话中');
                  ctx._callback('fail', ctx.formatReason(30002));
                } else {
                  ctx.called = {
                    rtcAccountID: phone,
                    pbxUserId: phone,
                    userId: phone,
                    userName: phone
                  };
                  if (ctx.server.checkUserStatus(ctx.called) || !vThis.isActive()) {
                    ctx.callout = true;
                    ctx.connect(false, false, true);
                  } else {
                    ctx.logger.warn('对方繁忙');
                    ctx._callback('fail', ctx.formatReason(10007));
                  }
                }
              }
            },
            hangup: function (ctx) {
              //挂断通话
              return function () {
                return this.check(function (e) {
                  ctx.isIosAuthen = false;
                  ctx.server.sendCommand('hangup', {
                    callId: e.callId
                  })
                })
              }
            },
            accept: function (ctx) {
              //接受通话
              return function () {
                return this.check(function (e) {
                  ctx._callback('waiting', ctx.formatReason(10021));
                  ctx.callout = false;
                  ctx.connect(false, false, false);
                })
              }
            },
            refuse: function (ctx) {
              //拒接通话
              return function () {
                return this.check(function (e) {
                  ctx.isRefuse = true;
                  ctx.server.sendCommand('answer_petra_audio', {
                    result: -1,
                    callId: e.callId,
                    to: this.getToUser(),
                    from: this.getFromUser()
                  });
                })
              }
            },
            sendDTMF: function (ctx) {
              //发送DTMF
              return function (key) {
                return this.check(function (e) {
                  if (ctx.dtmf) {
                    //400表示曲目时长 50为间隔时长，单位毫秒
                    if (key) {
                      ctx.logger.warn('按键：' + key);
                      if(ctx.dtmf.canInsertDTMF) ctx.dtmf.insertDTMF(key, ctx.config.dtmfDuration, ctx.config.dtmfBetweenTimes);
                      else ctx.logger.warn('不能发送DTMF');
                    }
                  } else {
                    ctx._callback('fail', _.extend(ctx.formatReason(10030), {
                      msgData: {
                        tip: '通道未连接成功'
                      }
                    }))
                  }
                })
              }
            },
            mutedRemote: function (ctx) {
              //静音远程流
              return function (status) {
                return this.check(function () {
                  if (ctx.remoteAudio) {
                    ctx.remoteAudio.muted = status != undefined ? status : !ctx.remoteAudio.muted;
                  }
                })
              }
            },
            getRemoteAudio: function (ctx) {
              return function () {
                return ctx.remoteAudio;
              }
            },
            getLocalAudio: function (ctx) {
              return function () {
                return ctx.localAudio;
              }
            }
          }
        }),
        NetVideoChat = Define({
          /**
           * 网络视频通话
           */
          extends: {
            name: SenceBase,
            arguments: function (ctx) {
              //向父类传递参数
              return [ctx, 'netVideoChat']
            },
            super: function (ctx) {
              //父类执行后回调
              ctx.bindCommandEvent();
            }
          },
          inited: function (ctx) {
            //实例初始化
            return function (config, server) {
              _.extend(ctx.config, config || {});
              ctx.server = server;
              ctx.logger = server.getLogger();
            }
          },
          events: _C.NETVIDEOCHAT_EVENT,
          private: {
            config: {
              autoAccept: false, //自动应答
            },
            server: null, //服务上下文对象
            logger: null, //日记对象
            isRefuse: false, //是否拒接
            isCalled: false, //是否被叫
            callout: true, //呼出
            remoteSdp: '', //远程SDP
            called: null, //被叫信息
            peer: null, //通道对象
            waitTimer: null,
            virtualPhoto: null, //虚拟头像
            callType: '',
            optional: {
              optional: [{
                DtlsSrtpKeyAgreement: true
              }
            ],
            },
            bindTrackEvent: function (ctx) {
              //绑定轨道事件
              return function (stream, mark) {
                var vThis = this;
                var events = {
                  ended: function (e, mark) {
                    vThis._callback('track', _U.netVideoChat(10028, {
                      callId: ctx.getCallId(),
                      from: mark,
                      state: 'ended'
                    }));
                  },
                  mute: function (e, mark) {
                    vThis._callback('track', _U.netVideoChat(10028, {
                      callId: ctx.getCallId(),
                      from: mark,
                      state: 'mute'
                    }));
                  },
                  unmute: function (e, mark) {
                    vThis._callback('track', _U.netVideoChat(10028, {
                      callId: ctx.getCallId(),
                      from: mark,
                      state: 'unmute'
                    }));
                  }
                };
                if (stream) {
                  stream.getTracks().forEach(function (track) {
                    for (var key in events) {
                      track.addEventListener(key, {
                        handleEvent: function (e) {
                          this.callback.call(this.handler, e, this.mark);
                        },
                        handler: track,
                        mark: mark,
                        callback: events[key]
                      });
                    }
                  });
                }
              }
            },
            bindCommandEvent: function (ctx) {
              //绑定指令事件
              return function () {
                var vThis = this;
                vThis.server.bindCommand(ctx, {
                  'offer_petra_video': function (e) {
                    vThis.remoteSdp = e.data;
                    vThis.called = {
                      rtcAccountID: e.from.rtcCaller,
                      userId: e.from.rtcCaller
                    };
                    vThis.callType = 'video';
                    vThis.callout = false;
                    vThis.isCalled = true;
                    ctx.init({
                      user: vThis.called,
                      callId: e.callId
                    });
                    if (!ctx.isActive()&&!e.reinvite&&!e.reconnect) {
                      vThis.optional.optional[0].DtlsSrtpKeyAgreement = /a=fingerprint/.test(e.data.sdp);
                      vThis.logger.info('Volte通话来电');
                      vThis.isRefuse = false;
                      vThis._callback('ring', _U.netVideoChat(20001, {
                        user: e.from,
                        callId: e.callId
                      }));
                      if (vThis.config.autoAccept) {
                        //自动应答
                        vThis.logger.info('已自动应答');
                        ctx.accept();
                      }
                    } else {
                      vThis.connect(e.reconnect, e.reinvite, false);
                    }
                  },
                  'resp_offer_petra_video': function (e, isOk) {
                    if (isOk) {
                      vThis._callback('status', _U.netVideoChat(10002, {
                        user: e.member,
                        callId: e.callId
                      }));
                      ctx.init({
                        user: vThis.called,
                        callId: e.callId
                      });
                      ctx.startHeart();
                    } else {
                      vThis.logger.warn('呼叫失败');
                      vThis._callback('fail', _U.netVideoChat(10003, {
                        tip: e.tip
                      }));
                    }
                  },
                  'answer_petra_video': function (e, isOk) {
                    if (isOk) {
                      vThis.peer.remote(e.data);
                    }
                  },
                  'resp_answer_petra_video': function (e, isOk) {
                    if (isOk) {
                      if (vThis.isRefuse) {
                        vThis.isRefuse = false;
                        vThis._callback('hangup', _U.netVideoChat(20004));
                        ctx.destory();
                      } else {
                        ctx.startHeart();
                      }
                    } else {
                      vThis.logger.warn('接听失败');
                      vThis._callback('fail', _U.netVideoChat(20003, {
                        tip: e.tip
                      }));
                    }
                  },
                  'answer_petra_audio': function(e, isOk){
                    if(isOk&&ctx.isActive()&&ctx.checkCallId(e.callId)){
                      vThis.peer.remote(e.data);
                    }
                  },
                  'evt_reconnect': function (e) {
                    ctx.check(function (e) {
                      if (!ctx.isActive()) {
                        //未接通时不重连
                        return;
                      }
                      vThis.logger.warn('重连通话');
                      if (vThis.isCalled) {
                        //被叫时
                        vThis.server.sendCommand('called_reconnect_petra_video', {
                          callId: e.callId,
                          to: {
                            rtcCaller: vThis.called.rtcAccountID
                          },
                          from: ctx.getFromUser()
                        })
                      } else {
                        vThis.connect(true, false, true);
                      }
                    })
                  },
                  'resp_hangup': function (e, isOk) {
                    if (!isOk) {
                      vThis.logger.warn('主动挂断失败');
                      vThis._callback('fail', _U.netVideoChat(30004, {
                        tip: e.tip
                      }));
                    }
                  },
                  'evt_call_end': function (e, isOk) {
                    if (isOk) {
                      var tCode = 10011;
                      ctx.destory();
                      if (e.code == 'offline') {
                        tCode = 10005;
                      } else if (e.code == 'reject') {
                        tCode = 10004;
                      } else if (e.code == 'busy' || e.code == 'audioDisconnect' || e.code == 'force') {
                        tCode = 10006;
                      } else if (e.code == 'timeout') {
                        tCode = 10007;
                      } else {
                        vThis._callback('hangup', _U.netVideoChat(tCode, e));
                        return;
                      }
                      vThis._callback('fail', _U.netVideoChat(tCode, {
                        code: e.code,
                        tip: e.tip
                      }));
                    }
                  }
                })
              }
            },
            formatReason: function (ctx) {
              //格式化事件数据
              return function (code, reason) {
                return _U.callbackMsg(code, 'NetVideoChat', 'netVideochat');
              }
            },
            initWait: function (ctx) {
              return function () {
                var tTimer = new Timer({
                  duration: 3000
                });
                tTimer._postData({
                  tip: '呼叫等待中'
                });
                tTimer.start();
                this.waitTimer = tTimer;
              }
            },
            connect: function(ctx){
              return function(isReconnet, isReinvite, isOffer){
                var vThis = this;
                var init = function(stream){
                  if(vThis.virtualPhoto) ctx.setVirtualPhoto(vThis.virtualPhoto);
                  vThis._callback('status', _U.mediaDevice(10002));
                  vThis.peer = ctx.createPipe(vThis.called.rtcAccountID, stream, vThis.optional);
                  vThis.peer.onReady(function (e) {
                    vThis._callback('status', vThis.formatReason(10004));
                    if(isOffer) vThis.peer.offer();
                    else vThis.peer.answer(vThis.remoteSdp);
                  }).onConnect(function (e) {
                    if(vThis.waitTimer) vThis.waitTimer.stop();
                    vThis.bindTrackEvent(stream, 'local');
                    vThis.bindTrackEvent(e.msgData.stream, 'remote');
                    vThis._callback('connect', _.extend(vThis.formatReason(10014), {
                      msgData: {
                        list: [{
                            type: 'local',
                            user: ctx.getFromUser(),
                            stream: stream
                          },
                          {
                            type: 'remote',
                            user: ctx.getToUser(),
                            stream: e.msgData.stream
                          }
                        ]
                      }
                    }));
                    if(!isReconnet) ctx.startTimer();
                  }).onStatus(function (e) {
                    vThis.logger.info('通道状态：' + _.toJson(e));
                  }).onFail(function (e) {
                    vThis.logger.info('通道失败：' + _.toJson(e));
                    vThis._callback('fail', vThis.formatReason(10028));
                    ctx.hangup();
                  }).onOffer(function (e) {
                    var tSerialId = '';
                    var command = 'offer_petra_video';
                    var data = {
                      to: {
                        rtcCalled: vThis.called.rtcAccountID
                      },
                      from: ctx.getFromUser(),
                      data: e.msgData.sdp
                    };
                    if(isReconnet){
                      data.reconnect = isReconnet;
                      data.callId = ctx.getCallId();
                    }else{
                      vThis._callback('waiting', vThis.formatReason(10025));
                    }
                    tSerialId = vThis.server.sendCommand(command, data);
                    ctx.setSerialId(tSerialId);
                  }).onAnswer(function (e) {
                    var data = {
                      answerCode: 0,
                      callId: ctx.getCallId(),
                      to: ctx.getToUser(),
                      from: ctx.getFromUser(),
                      data: e.msgData.sdp
                    };
                    if(isReinvite) data.reinvite = isReinvite;
                    vThis.server.sendCommand('answer_petra_video', data);
                  });
                };
                if(ctx.isActive()&&!isReconnet){
                  vThis.logger.warn('网络视频通话繁忙');
                  vThis._callback('fail', _U.netVideoChat(30002, {
                    callId: ctx.getCallId()
                  }));
                }else if(isReconnet) {
                  vThis.logger.info('旧通道状态');
                  console.log(vThis.peer.getConnect().connectionState);
                  if (vThis.peer.isConnect()) {
                    vThis.logger.info('主动断开旧通道');
                    vThis.peer.disconnect(false);
                  } else vThis.logger.info('旧通道已断开');
                  if(ctx.isActive()){
                    init(Device.getStream());
                  }else {
                    Device.open(true, true).then(function(stream){
                      init(stream);
                    }).catch(function (e) {
                      vThis._callback('fail', _U.mediaDevice(10003, {
                        type: e.name,
                        message: e.message
                      }))
                    });
                  }
                }else {
                  var cons = Device.getMediaConstraint();
                  vThis._callback('status', _U.mediaDevice(10001, cons));
                  Device.open(true, true).then(function(stream){
                    vThis._callback('waiting', vThis.formatReason(10022));
                    vThis._callback('waiting', vThis.formatReason(10023));
                    init(stream);
                  }).catch(function (e) {
                    vThis._callback('fail', _U.mediaDevice(10003, {
                      type: e.name,
                      message: e.message
                    }))
                  });
                } 
              } 
            }
          },
          public: {
            call: function (ctx) {
              //呼叫对方
              return function (phone) {
                var vThis = this;
                if(!ctx.server.isLive()){
                  ctx._callback('fail', _U.serverManager(10019));
                  return;
                }
                ctx.initWait();
                ctx._callback('status', ctx.formatReason(10001));
                if (this.isActive()) {
                  ctx.logger.warn('已在通话中');
                  ctx._callback('fail', ctx.formatReason(30002));
                } else {
                  ctx.called = {
                    rtcAccountID: phone,
                    pbxUserId: phone,
                    userId: phone,
                    userName: phone
                  };
                  if (ctx.server.checkUserStatus(ctx.called) || !vThis.isActive()) {
                    ctx.callout = true;
                    ctx.isCalled = false;
                    ctx.connect(false, false, true);
                  } else {
                    ctx.logger.warn('对方繁忙');
                    ctx._callback('fail', ctx.formatReason(10007));
                  }
                }
              }
            },
            hangup: function (ctx) {
              //挂断通话
              return function () {
                return this.check(function (e) {
                  ctx.server.sendCommand('hangup', {
                    callId: e.callId
                  })
                })
              }
            },
            accept: function (ctx) {
              //接受通话
              return function () {
                return this.check(function (e) {
                  ctx._callback('waiting', ctx.formatReason(10021));
                  ctx.callout = false;
                  ctx.connect(false, false, false);
                })
              }
            },
            refuse: function (ctx) {
              return function () {
                return this.check(function (e) {
                  ctx.isRefuse = true;
                  ctx.server.sendCommand('answer_petra_video', {
                    result: -1,
                    callId: e.callId,
                    to: this.getToUser(),
                    from: this.getFromUser()
                  });
                })
              }
            },
            shareScreen: function(ctx){
              return function(){
                return this.check(function(e){
                  return Device.shareScreen(ctx.peer.getConnect());
                })
              }
            },
            shareResource: function(ctx){
              return function(resource, type, show, keep, loop){
                return this.check(function(e){
                  return Device.shareResource(ctx.peer.getConnect(), resource, type, show, keep, loop);
                })
              }
            },
            shareScreenshot: function(ctx){
              return function(show){
                return this.check(function(e){
                  return Device.shareScreenshot(ctx.peer.getConnect(), show);
                })
              }
            },
            switchShareSound: function(ctx){
              return function(){
                return Device.switchShareSound(ctx.peer.getConnect());
              }
            },
            setVirtualPhoto: function(ctx){
              return function(url){
                return _.promise(function(ok, fail){
                  if(url){
                    ctx.virtualPhoto = url;
                    if(!Device.isOpen()){
                      ok();
                    }else{
                      Device.shareResource(ctx.peer?ctx.peer.getConnect():null, url, 0, true, true).then(ok).catch(fail);
                    }
                  }else fail('资源地址为空');
                }, this);
              }
            },
            cancelVirtualPhoto: function(ctx){
              return function(){
                if(ctx.virtualPhoto){
                  Device.cancelShare();
                  ctx.virtualPhoto = '';
                }
              }
            },
            isVirtualPhoto: function(ctx){
              return function(){
                return !!ctx.virtualPhoto;
              }
            }
          }
        }),
        VolteCall = Define({
          /**
           * Volte通话
           */
           extends: {
            name: SenceBase,
            arguments: function (ctx) {
              //向父类传递参数
              return [ctx, 'volteCall']
            },
            super: function (ctx) {
              //父类执行后回调
              ctx.bindCommandEvent();
            }
          },
          inited: function (ctx) {
            //实例初始化
            return function (config, server) {
              _.extend(ctx.config, config || {});
              ctx.server = server;
              ctx.logger = server.getLogger();
            }
          },
          events: _C.VOLTECALL_EVENT,
          private: {
            config: {
              autoAccept: false, //自动应答
              inline: true,
            },
            server: null, //服务上下文对象
            logger: null, //日记对象
            isCalled: false, //是否被叫
            isRefuse: false, //是否拒接
            remoteSdp: '', //远程SDP
            called: null, //被叫信息
            callType: '', //呼叫类型
            callout: true, //呼出
            peer: null, //通道对象
            waitTimer: null,
            virtualPhoto: null, //虚拟头像
            optional: {
              optional: [{
                DtlsSrtpKeyAgreement: true
              }]
            },
            dtmf: null, //DTMF发送对象
            remoteAudio: null,
            localAudio: null,
            isIosAuthen: false, //ios媒体播放授权
            isDeviceReady: true,
            setVideoCall: function(ctx){
              return function(){
                this.callType = 'video';
              }
            },
            setAudioCall: function(ctx){
              return function(){
                this.callType = 'audio';
              }
            },
            isVideoCall: function(ctx){
              return function(){
                return this.callType=='video';
              }
            },
            bindTrackEvent: function (ctx) {
              //绑定轨道事件
              return function (stream, mark) {
                var vThis = this;
                var events = {
                  ended: function (e, mark) {
                    vThis._callback('track', _U.netVideoChat(10028, {
                      callId: ctx.getCallId(),
                      from: mark,
                      state: 'ended'
                    }));
                  },
                  mute: function (e, mark) {
                    vThis._callback('track', _U.netVideoChat(10028, {
                      callId: ctx.getCallId(),
                      from: mark,
                      state: 'mute'
                    }));
                  },
                  unmute: function (e, mark) {
                    vThis._callback('track', _U.netVideoChat(10028, {
                      callId: ctx.getCallId(),
                      from: mark,
                      state: 'unmute'
                    }));
                  }
                };
                if (stream) {
                  stream.getTracks().forEach(function (track) {
                    for (var key in events) {
                      track.addEventListener(key, {
                        handleEvent: function (e) {
                          this.callback.call(this.handler, e, this.mark);
                        },
                        handler: track,
                        mark: mark,
                        callback: events[key]
                      });
                    }
                  });
                }
              }
            },
            bindCommandEvent: function (ctx) {
              //绑定指令事件
              return function () {
                var vThis = this;
                vThis.server.bindCommand(ctx, {
                  'offer_petra_video': function (e) {
                    vThis.remoteSdp = e.data;
                    vThis.called = {
                      rtcAccountID: e.from.rtcCaller,
                      userId: e.from.rtcCaller
                    };
                    vThis.callType = 'video';
                    vThis.callout = false;
                    vThis.isCalled = true;
                    ctx.init({
                      user: vThis.called,
                      callId: e.callId
                    });
                    if (!ctx.isActive()&&!e.reinvite&&!e.reconnect) {
                      vThis.optional.optional[0].DtlsSrtpKeyAgreement = /a=fingerprint/.test(e.data.sdp);
                      vThis.logger.info('Volte通话来电');
                      vThis.isRefuse = false;
                      vThis._callback('ring', _U.netVideoChat(20001, {
                        user: e.from,
                        callId: e.callId
                      }));
                      if (vThis.config.autoAccept) {
                        //自动应答
                        vThis.logger.info('已自动应答');
                        ctx.accept();
                      }
                    } else {
                      vThis.connect(e.reconnect, e.reinvite, false);
                    }
                  },
                  'resp_offer_petra_video': function (e, isOk) {
                    if (isOk) {
                      vThis._callback('status', _U.netVideoChat(10002, {
                        user: e.member,
                        callId: e.callId
                      }));
                      ctx.init({
                        user: vThis.called,
                        callId: e.callId
                      });
                      ctx.startHeart();
                    } else {
                      vThis.logger.warn('呼叫失败');
                      vThis._callback('fail', _U.netVideoChat(10003, {
                        tip: e.tip
                      }));
                    }
                  },
                  'answer_petra_video': function (e, isOk) {
                    if (isOk&&ctx.isActive()&&ctx.checkCallId(e.callId)) {
                      if(!/m=video \d+/.test(e.data.sdp)){
                        //升级视频失败
                        // vThis._callback('fail', _U.netVideoChat(10030, {
                        //   tip: '语音升级视频通话失败'
                        // }));
                        ctx.convertToAudioCall();
                      }else {
                        vThis.setVideoCall();
                        vThis.peer.remote(e.data);
                      }
                    }
                  },
                  'resp_answer_petra_video': function (e, isOk) {
                    if (isOk) {
                      if (vThis.isRefuse) {
                        vThis.isRefuse = false;
                        vThis._callback('hangup', _U.netVideoChat(20004));
                        ctx.destory();
                      } else {
                        ctx.startHeart();
                      }
                    } else {
                      vThis.logger.warn('接听失败');
                      vThis._callback('fail', _U.netVideoChat(20003, {
                        tip: e.tip
                      }));
                    }
                  },
                  'offer_petra_audio': function(e, isOk){
                    vThis.remoteSdp = e.data;
                    vThis.called = {
                      rtcAccountID: e.from.rtcCaller,
                      userId: e.from.rtcCaller
                    };
                    vThis.callType = 'audio';
                    vThis.callout = false;
                    vThis.isCalled = true;
                    ctx.init({
                      user: vThis.called,
                      callId: e.callId
                    });
                    if (!ctx.isActive()) {
                      vThis.isRefuse = false;
                      vThis.optional.optional[0].DtlsSrtpKeyAgreement = /a=fingerprint/.test(e.data.sdp);
                      vThis.logger.info('Volte通话来电');
                      vThis._callback('ring', _.extend(vThis.formatReason(10013), {
                        msgData: {
                          user: e.from,
                          callId: e.callId
                        }
                      }));
                      if (vThis.config.autoAccept) {
                        //自动应答
                        vThis.logger.info('已自动应答');
                        ctx.accept();
                      }
                    } else {
                      vThis.connect(e.reconnect, e.reinvite, false);
                    }
                  },
                  'resp_offer_petra_audio': function(e, isOk){
                    if (isOk) {
                      vThis._callback('status', _U.netVideoChat(10002, {
                        user: e.member,
                        callId: e.callId
                      }));
                      ctx.init({
                        user: vThis.called,
                        callId: e.callId
                      });
                      ctx.startHeart();
                    } else {
                      vThis.logger.warn('呼叫失败');
                      vThis._callback('fail', _U.netVideoChat(10003, {
                        tip: e.tip
                      }));
                    }
                  },
                  'answer_petra_audio': function(e, isOk){
                    if(isOk&&ctx.isActive()&&ctx.checkCallId(e.callId)){
                      if(/m=video [^0]/.test(e.data.sdp)){
                        //降级失败
                        // vThis._callback('fail', _U.netVideoChat(10029, {
                        //   tip: '视频降级语音通话失败'
                        // }));
                        ctx.convertToVideoCall();
                      }else {
                        vThis.setAudioCall();
                        vThis.peer.remote(e.data);
                      }
                    }
                  },
                  'resp_answer_petra_audio': function (e, isOk) {
                    if (isOk) {
                      if (vThis.isRefuse) {
                        vThis.isRefuse = false;
                        vThis._callback('hangup', _U.netVideoChat(20004));
                        ctx.destory();
                      } else {
                        ctx.startHeart();
                      }
                    } else {
                      vThis.logger.warn('接听失败');
                      vThis._callback('fail', _U.netVideoChat(20003, {
                        tip: e.tip
                      }));
                    }
                  },
                  'evt_reconnect': function (e) {
                    ctx.check(function (e) {
                      if (!ctx.isActive()) {
                        //未接通时不重连
                        return;
                      }
                      vThis.logger.warn('重连通话');
                      if (vThis.isCalled) {
                        //被叫时
                        vThis.server.sendCommand(vThis.isVideoCall()?'called_reconnect_petra_video':'called_reconnect_petra_audio', {
                          callId: e.callId,
                          to: {
                            rtcCaller: vThis.called.rtcAccountID
                          },
                          from: ctx.getFromUser()
                        })
                      } else {
                        vThis.connect(true, false, true);
                      }
                    })
                  },
                  'resp_hangup': function (e, isOk) {
                    if (!isOk) {
                      vThis.logger.warn('主动挂断失败');
                      vThis._callback('fail', _U.netVideoChat(30004, {
                        tip: e.tip
                      }));
                    }
                  },
                  'evt_call_end': function (e, isOk) {
                    if (isOk) {
                      var tCode = 10011;
                      vThis.callType = '';
                      ctx.destory();
                      if (e.code == 'offline') {
                        tCode = 10005;
                      } else if (e.code == 'reject') {
                        tCode = 10004;
                      } else if (e.code == 'busy' || e.code == 'audioDisconnect' || e.code == 'force') {
                        tCode = 10006;
                      } else if (e.code == 'timeout') {
                        tCode = 10007;
                      } else {
                        vThis._callback('hangup', _U.netVideoChat(tCode, e));
                        return;
                      }
                      vThis._callback('fail', _U.netVideoChat(tCode, {
                        code: e.code,
                        tip: e.tip
                      }));
                    }
                  }
                })
              }
            },
            formatReason: function (ctx) {
              //格式化事件数据
              return function (code, reason) {
                return _U.callbackMsg(code, 'VolteCall', 'volteCall');
              }
            },
            initWait: function (ctx) {
              return function () {
                var tTimer = new Timer({
                  duration: 3000
                });
                tTimer._postData({
                  tip: '呼叫等待中'
                });
                tTimer.start();
                this.waitTimer = tTimer;
              }
            },
            initDTMF: function (ctx) {
              //初始化DTMF
              return function () {
                var tPeer = this.peer.getConnect();
                if (tPeer.getSenders) {
                  var dtmf = tPeer.getSenders().find(function(sender){
                    return sender.track.kind=='audio'&&sender.dtmf;
                  });
                  if(dtmf) this.dtmf = dtmf.dtmf;
                }
                console.log('DTMF', this.dtmf);
                _.bindEvent(this.dtmf, 'tonechange', function (e) {
                  console.log('DTMF', e);
                });
              }
            },
            connect: function(ctx){
              return function(isReconnet, isReinvite, isOffer){
                var vThis = this;
                var reconnect = function(e){
                  if(vThis.server.isLive()&&ctx.getCallId()){
                    vThis.logger.warn('服务已就绪, 开始重连通话');
                    if(vThis.isVideoCall()){
                      vThis.logger.log('恢复视频通话');
                    }else{
                      vThis.logger.log('恢复语音通话');
                    }
                    vThis.connect(true, false, true);
                  }
                  vThis.server._unbind('ready', reconnect);
                };
                var init = function(stream){
                  if(vThis.isVideoCall() && vThis.virtualPhoto) ctx.setVirtualPhoto(vThis.virtualPhoto);
                  vThis._callback('status', _U.mediaDevice(10002));
                  vThis.peer = ctx.createPipe(vThis.called.rtcAccountID, stream, vThis.optional);
                  vThis.peer.onReady(function (e) {
                    vThis._callback('status', vThis.formatReason(10004));
                    if(isOffer) vThis.peer.offer(true);
                    else vThis.peer.answer(vThis.remoteSdp);
                    vThis.isDeviceReady = true;
                  }).onConnect(function (e) {
                    var track = null, localWidth = 0, localHeight = 0, remoteWidth = 0, remoteHeight = 0;
                    if(!vThis.isVideoCall()) vThis.initDTMF();
                    if(vThis.waitTimer) vThis.waitTimer.stop();
                    if(stream.getVideoTracks().length>0) {
                      track = stream.getVideoTracks()[0].getSettings();
                      localWidth = track.width;
                      localHeight = track.height;
                    }
                    if(e.msgData.stream.getVideoTracks().length>0) {
                      track = e.msgData.stream.getVideoTracks()[0].getSettings();
                      remoteWidth = track.width;
                      remoteHeight = track.height;
                    }
                    vThis.bindTrackEvent(stream, 'local');
                    vThis.bindTrackEvent(e.msgData.stream, 'remote');
                    vThis._callback('callTypeChange', {type: vThis.callType});
                    vThis._callback('connect', _.extend(vThis.formatReason(10014), {
                      msgData: {
                        list: [{
                            type: 'local',
                            user: ctx.getFromUser(),
                            stream: stream,
                            width: localWidth,
                            height: localHeight
                          },
                          {
                            type: 'remote',
                            user: ctx.getToUser(),
                            stream: e.msgData.stream,
                            width: remoteWidth,
                            height: remoteHeight
                          }
                        ],
                        callType: vThis.callType
                      }
                    }));
                    if(!isReconnet) ctx.startTimer();
                    if (vThis.config.inline && !vThis.isVideoCall()) {
                      //内置播放
                      vThis.inlinePlay(stream, e.msgData.stream);
                    }
                  }).onVideoState(function(e){
                    vThis._callback('remoteVideoState', vThis.formatReason(10028, e.msgData));
                  }).onAudioState(function(e){
                    vThis._callback('remoteAudioState', vThis.formatReason(10028, e.msgData));
                  }).onStatus(function (e) {
                    vThis.logger.info('通道状态：' + _.toJson(e));
                  }).onFail(function (e) {
                    vThis.logger.info('通道失败：' + _.toJson(e));
                    vThis._callback('fail', vThis.formatReason(10028));
                    ctx.hangup();
                  }).onOffer(function (e) {
                    var tSerialId = '';
                    var command = vThis.isVideoCall()?'offer_petra_video':'offer_petra_audio';
                    var data = {
                      to: {
                        rtcCalled: vThis.called.rtcAccountID
                      },
                      from: ctx.getFromUser(),
                      data: e.msgData.sdp
                    };
                    if(isReconnet){
                      data.reconnect = isReconnet;
                      data.callId = ctx.getCallId();
                    }else{
                      vThis._callback('waiting', vThis.formatReason(10025));
                    }
                    tSerialId = vThis.server.sendCommand(command, data);
                    ctx.setSerialId(tSerialId);
                  }).onAnswer(function (e) {
                    var data = {
                      callId: ctx.getCallId(),
                      to: ctx.getToUser(),
                      from: ctx.getFromUser(),
                      data: e.msgData.sdp
                    };
                    if(vThis.isVideoCall()) data.answerCode = 0;
                    else data.result = '000';
                    if(isReinvite) data.reinvite = isReinvite;
                    vThis.server.sendCommand(vThis.isVideoCall()?'answer_petra_video':'answer_petra_audio', data);
                  }).onDisconnect(function(e){
                    vThis.logger.warn(e);
                    ctx.stopHeart();
                    if(ctx.getCallId()){
                      vThis.logger.warn('网络中断，通话保持');
                      vThis.server._unbind('ready', reconnect);
                      vThis.server._bind('ready', reconnect);
                    }
                  });
                };
                if(ctx.isActive()&&!isReconnet){
                  vThis.logger.warn('Volte通话繁忙');
                  vThis._callback('fail', _U.volteCall(30002, {
                    callId: ctx.getCallId()
                  }));
                }else if(isReconnet) {
                  if(!vThis.isDeviceReady){
                    vThis.logger.info('上一次请求未处理完，过滤相同的请求处理');
                    return;
                  }
                  vThis.isDeviceReady = false;
                  vThis.logger.info('旧通道状态');
                  console.log(vThis.peer.getConnect().connectionState);
                  if (vThis.peer) {
                    vThis.logger.info('主动断开旧通道');
                    vThis.peer.disconnect(false);
                  } else vThis.logger.info('旧通道已断开');
                  Device.open(vThis.isVideoCall(), true).then(function(stream){
                    init(stream);
                  }).catch(function (e) {
                    vThis._callback('fail', _U.mediaDevice(10003, {
                      type: e.name,
                      message: e.message
                    }))
                  });
                }else {
                  var cons = Device.getMediaConstraint();
                  vThis._callback('status', _U.mediaDevice(10001, cons));
                  Device.open(vThis.isVideoCall(), true).then(function(stream){
                    vThis._callback('waiting', vThis.formatReason(10022));
                    vThis._callback('waiting', vThis.formatReason(10023));
                    init(stream);
                  }).catch(function (e) {
                    vThis._callback('fail', _U.mediaDevice(10003, {
                      type: e.name,
                      message: e.message
                    }))
                  });
                } 
              } 
            },
            inlinePlay: function (ctx) {
              //内置播放音频
              return function (local, remote) {
                var vThis = this;
                if (vThis.localAudio) {
                  if (!vThis.localAudio.paused) vThis.localAudio.pause();
                  vThis.localAudio = null;
                }
                if (vThis.remoteAudio) {
                  if (!vThis.remoteAudio.paused) vThis.remoteAudio.pause();
                  vThis.remoteAudio = null;
                }
                var localAudio = new Audio();
                var remoteAudio = new Audio();
                localAudio.srcObject = local;
                remoteAudio.srcObject = remote;
                localAudio.autoplay = true;
                remoteAudio.autoplay = true;
                localAudio.muted = true;
                localAudio.setAttribute('playsinline', true);
                localAudio.setAttribute('webkit-playsinline', true);
                remoteAudio.setAttribute('playsinline', true);
                remoteAudio.setAttribute('webkit-playsinline', true);
                localAudio.onpause = function () {
                  vThis.logger.info('本地音频结束播放');
                };
                localAudio.onplay = function () {
                  vThis.logger.info('本地音频开始播放');
                };
                localAudio.oncanplay = function () {
                  vThis.logger.info('本地音频就绪');
                  localAudio.play();
                };
                remoteAudio.onpause = function () {
                  vThis.logger.info('远程音频结束播放');
                };
                remoteAudio.onplay = function () {
                  vThis.logger.info('远程音频开始播放');
                };
                remoteAudio.oncanplay = function () {
                  vThis.logger.info('远程音频就绪');
                  remoteAudio.play();
                };
                if(_.isIos()) {
                  localAudio.play();
                  remoteAudio.play();
                }
                local.oninactive=function () {
                  vThis.logger.info('本地流已停止');
                  if (!localAudio.paused) localAudio.pause();
                };
                local.onactive=function(){
                  vThis.logger.info('本地流已就绪');
                  localAudio.play();
                };
                remote.oninactive=function () {
                  vThis.logger.info('远程流已停止');
                  if (!remoteAudio.paused) remoteAudio.pause();
                };
                vThis.localAudio = localAudio;
                vThis.remoteAudio = remoteAudio;
              }
            }
          },
          public: {
            call: function (ctx) {
              //呼叫对方
              return function (phone) {
                var vThis = this;
                if(!ctx.server.isLive()){
                  ctx._callback('fail', _U.serverManager(10019));
                  return;
                }
                ctx.initWait();
                ctx._callback('status', ctx.formatReason(10001));
                if (this.isActive()) {
                  ctx.logger.warn('已在通话中');
                  ctx._callback('fail', ctx.formatReason(30002));
                } else {
                  ctx.called = {
                    rtcAccountID: phone,
                    pbxUserId: phone,
                    userId: phone,
                    userName: phone
                  };
                  if (ctx.server.checkUserStatus(ctx.called) || !vThis.isActive()) {
                    ctx.setAudioCall();
                    ctx.callout = true;
                    ctx.isCalled = false;
                    ctx.connect(false, false, true);
                  } else {
                    ctx.logger.warn('对方繁忙');
                    ctx._callback('fail', ctx.formatReason(10007));
                  }
                }
              }
            },
            chat: function(ctx){
              return function(phone){
                var vThis = this;
                if(!ctx.server.isLive()){
                  ctx._callback('fail', _U.serverManager(10019));
                  return;
                }
                ctx.initWait();
                ctx._callback('status', ctx.formatReason(10001));
                if (this.isActive()) {
                  ctx.logger.warn('已在通话中');
                  ctx._callback('fail', ctx.formatReason(30002));
                } else {
                  ctx.called = {
                    rtcAccountID: phone,
                    pbxUserId: phone,
                    userId: phone,
                    userName: phone
                  };
                  if (ctx.server.checkUserStatus(ctx.called) || !vThis.isActive()) {
                    ctx.setVideoCall();
                    ctx.callout = true;
                    ctx.isCalled = false;
                    ctx.connect(false, false, true);
                  } else {
                    ctx.logger.warn('对方繁忙');
                    ctx._callback('fail', ctx.formatReason(10007));
                  }
                }
              }
            },
            hangup: function (ctx) {
              //挂断通话
              return function () {
                return this.check(function (e) {
                  ctx.server.sendCommand('hangup', {
                    callId: e.callId
                  })
                })
              }
            },
            accept: function (ctx) {
              //接受通话
              return function () {
                return this.check(function (e) {
                  ctx._callback('waiting', ctx.formatReason(10021));
                  ctx.callout = false;
                  ctx.connect(false);
                })
              }
            },
            refuse: function (ctx) {
              return function () {
                return this.check(function (e) {
                  ctx.isRefuse = true;
                  ctx.server.sendCommand(ctx.isVideoCall()?'answer_petra_video':'answer_petra_audio', {
                    result: -1,
                    callId: e.callId,
                    to: this.getToUser(),
                    from: this.getFromUser()
                  });
                })
              }
            },
            shareScreen: function(ctx){
              return function(){
                return this.check(function(e){
                  return Device.shareScreen(ctx.peer.getConnect());
                })
              }
            },
            shareResource: function(ctx){
              return function(resource, type, show, keep, loop){
                return this.check(function(e){
                  return Device.shareResource(ctx.peer.getConnect(), resource, type, show, keep, loop);
                })
              }
            },
            shareScreenshot: function(ctx){
              return function(show){
                return this.check(function(e){
                  return Device.shareScreenshot(ctx.peer.getConnect(), show);
                })
              }
            },
            switchShareSound: function(ctx){
              return function(){
                return Device.switchShareSound(ctx.peer.getConnect());
              }
            },
            setVirtualPhoto: function(ctx){
              return function(url){
                return _.promise(function(ok, fail){
                  if(url){
                    ctx.virtualPhoto = url;
                    if(!Device.isOpen()){
                      ok();
                    }else{
                      Device.shareResource(ctx.peer?ctx.peer.getConnect():null, url, 0, true, true).then(ok).catch(fail);
                    }
                  }else fail('资源地址为空');
                }, this);
              }
            },
            cancelVirtualPhoto: function(ctx){
              return function(){
                if(ctx.virtualPhoto){
                  Device.cancelShare();
                  ctx.virtualPhoto = '';
                }
              }
            },
            isVirtualPhoto: function(ctx){
              return function(){
                return !!ctx.virtualPhoto;
              }
            },
            convertToAudioCall: function(ctx){
              //转换为语音通话，降级
              return function(){
                return this.check(function(e){
                  if(e.active && ctx.isVideoCall()){
                    Device.cancelShare();
                    Device.open(false, true).then(function(){
                      ctx.setAudioCall();
                      ctx.connect(true, false, true);
                    }).catch(function (e) {
                      ctx._callback('fail', _U.mediaDevice(10003, {
                        type: e.name,
                        message: e.message
                      }))
                    });
                  }
                })
              }
            },
            convertToVideoCall: function(ctx){
              //转换为视频通话，升级
              return function(){
                return this.check(function(e){
                  if(e.active && !ctx.isVideoCall()){
                    Device.open(true, true).then(function(){
                      ctx.setVideoCall();
                      ctx.connect(true, false, true);
                    }).catch(function (e) {
                      ctx._callback('fail', _U.mediaDevice(10003, {
                        type: e.name,
                        message: e.message
                      }))
                    });
                  }
                });
              }
            },
            sendDTMF: function (ctx) {
              //发送DTMF
              return function (key) {
                return this.check(function (e) {
                  if(!ctx.isVideoCall()){
                    if (ctx.dtmf) {
                      //400表示曲目时长 50为间隔时长，单位毫秒
                      if (key) {
                        ctx.logger.warn('按键：' + key);
                        if(ctx.dtmf.canInsertDTMF) ctx.dtmf.insertDTMF(key, ctx.config.dtmfDuration, ctx.config.dtmfBetweenTimes);
                        else ctx.logger.warn('不能发送DTMF');
                      }
                    } else {
                      ctx._callback('fail', _.extend(ctx.formatReason(10030), {
                        msgData: {
                          tip: '通道未连接成功'
                        }
                      }))
                    }
                  }
                })
              }
            },
            mutedRemote: function (ctx) {
              //静音远程流
              return function (status) {
                return this.check(function () {
                  if (ctx.remoteAudio && !ctx.isVideoCall()) {
                    ctx.remoteAudio.muted = status != undefined ? status : !ctx.remoteAudio.muted;
                  }
                })
              }
            },
            getRemoteAudio: function (ctx) {
              return function () {
                return ctx.remoteAudio;
              }
            },
            getLocalAudio: function (ctx) {
              return function () {
                return ctx.localAudio;
              }
            },
            getCallType: function(ctx){
              return function(){
                return ctx.callType;
              }
            }
          }
        }),
        Whiteboard = Define({
          /**
           * 电子签名，白板
           */
          inited: function (ctx) {
            //实例初始化
            return function (config, server) {
              _.extend(ctx.config, config || {});
              ctx.server = server;
              ctx.logger = server.getLogger();
              if (!config.canvas && !(config.canvas instanceof HTMLCanvasElement)) ctx.canvas = document.createElement('canvas');
              else ctx.canvas = config.canvas;
              ctx.context = config.canvas.getContext('2d');
              if (config.width) ctx.canvas.width = config.width;
              if (config.height) ctx.canvas.height = config.height;
              ctx.canvas.style.background = config.background;
              ctx.bindCommandEvent();
            }
          },
          events: _C.WHITEBOARD_EVENT,
          private: {
            config: {
              background: 'white',
              color: 'black',
              lineWidth: 2,
              width: 200,
              height: 200,
              peer: null,
              canvas: null
            },
            hasShow: false,
            isFull: false,
            canvas: null,
            context: null,
            senders: null,
            tm: null,
            cacheStream: null,
            server: null, //服务上下文对象
            logger: null, //日记对象
            bindCommandEvent: function (ctx) {
              return function () {
                var vThis = this;
                var isStart = false;
                var ox = 0,
                  oy = 0;
                var ol = 0,
                  ot = 0;
                var points = [];
                var beginPoint = null;
                var start = function (e) {
                  isStart = true;
                  ox = e.layerX;
                  oy = e.layerY;
                  ol = vThis.canvas.offsetLeft;
                  ot = vThis.canvas.offsetTop;
                  if (vThis.canDraw()) {
                    beginPoint = {
                      x: ox - ol,
                      y: oy - ot
                    };
                    points.push(beginPoint);
                  }
                };
                var move = function (e) {
                  if (isStart) {
                    var x = e.layerX;
                    var y = e.layerY;
                    if (vThis.canDraw()) {
                      points.push({
                        x: x - ol,
                        y: y - ot
                      });
                      if (points.length > 3) {
                        var lastPoint = points.slice(-2);
                        var ctlPoint = lastPoint[0];
                        var endPoint = {
                          x: (lastPoint[0].x + lastPoint[1].x) / 2,
                          y: (lastPoint[0].y + lastPoint[1].y) / 2
                        };
                        drawLine(beginPoint, ctlPoint, endPoint);
                        beginPoint = endPoint;
                        points = points.slice(-3);
                      }
                    }
                  }
                };
                var end = function () {
                  isStart = false;
                  if (vThis.canDraw()) {
                    vThis.context.closePath();
                  }
                };
                var drawLine = function (start, ctl, end) {
                  vThis.context.beginPath();
                  vThis.context.moveTo(start.x, start.y);
                  vThis.context.strokeStyle = vThis.config.color;
                  vThis.context.lineWidth = vThis.config.lineWidth;
                  vThis.context.lineJoin = 'round';
                  vThis.context.lindCap = 'round';
                  vThis.context.quadraticCurveTo(ctl.x, ctl.y, end.x, end.y);
                  vThis.context.stroke();
                };
                var events = {
                  mouseup: end,
                  mousemove: move,
                  touchmove: move,
                  touchend: end
                };
                var events2 = {
                  mousedown: start,
                  touchstart: start
                };
                var z = function (handler, key, callback) {
                  handler.addEventListener(key, callback);
                };
                if (vThis.canvas) {
                  for (var key in events) {
                    z(document, key, events[key]);
                  }
                  for (var key in events2) z(vThis.canvas, key, events2[key]);
                }
              }
            },
            canDraw: function (ctx) {
              return function () {
                return this.canvas && this.context;
              }
            },
            makeStream: function (ctx) {
              return function (stream) {
                var that = this;
                var cav = that.canvas.cloneNode();
                var contx = cav.getContext('2d');
                var vd = null;
                var x = 0,
                  y = 0,
                  w = 0,
                  h = 0;
                var z = function () {
                  if (!that.canDraw()) return;
                  if(that.tm) clearTimeout(that.tm);
                  that.tm = setTimeout(z, 64);
                  contx.fillStyle = that.config.background;
                  contx.fillRect(0, 0, cav.width, cav.height);
                  contx.drawImage(that.canvas, 0, 0);
                  if (vd && that.hasShow) {
                    if (that.isFull) {
                      contx.drawImage(vd, 0, 0, cav.width, cav.height);
                      contx.fillStyle = that.config.background;
                      contx.fillRect(x, y, w, h);
                      contx.drawImage(that.canvas, x, y, w, h);
                    } else contx.drawImage(vd, x, y, w, h);
                  }
                };
                if (stream) {
                  vd = document.createElement('video');
                  vd.autoplay = true;
                  vd.srcObject = stream;
                  vd.muted = false;
                  stream.addEventListener('inactive', function () {
                    if (that.tm) clearTimeout(that.tm);
                  });
                  w = Math.floor(cav.width / 3);
                  h = (cav.width / 3) * 1.333;
                  x = cav.width - w - 5;
                  y = 5;
                }
                z();
                return cav.captureStream(32);
              }
            }
          },
          public: {
            clear: function (ctx) {
              //清空画板
              return function () {
                if (ctx.canDraw()) {
                  ctx.context.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
                  this.setBackground('white');
                }
              }
            },
            destory: function (ctx) {
              //销毁画板
              return function () {
                this.clear();
                this.cancelShare();
                ctx.context = null;
                ctx.canvas = null;
              }
            },
            toImage: function (ctx) {
              //生成签名图片
              return function (callback) {
                if (ctx.canDraw()) {
                  callback(ctx.canvas.toDataURL());
                }
              }
            },
            write: function (ctx) {
              //写文字，通过style调整字样式
              return function (text, option) {
                option = _.extend({
                  font: '16px 微软雅黑 bold',
                  color: 'black',
                  x: ctx.canvas.width / 2,
                  y: ctx.canvas.height / 2
                }, option || {});
                if (ctx.canDraw()) {
                  ctx.context.font = option.font;
                  ctx.context.fillStyle = option.color || ctx.config.color;
                  ctx.context.textAlign = 'center';
                  ctx.context.textBaseline = 'middle';
                  ctx.context.fillText(text, option.x, option.y);
                }
              }
            },
            setBackground: function (ctx) {
              //设置背景色
              return function (color) {
                if (ctx.canDraw() && color) {
                  ctx.canvas.style.background = color;
                  ctx.config.background = color;
                }
              }
            },
            setColor: function (ctx) {
              //设置前置色
              return function (color) {
                if (color) ctx.config.color = color;
              }
            },
            setLineWidth: function (ctx) {
              //设置线宽
              return function (width) {
                if (/^\d+$/.test(width)) ctx.config.lineWidth = width;
              }
            },
            getCanvas: function (ctx) {
              return function () {
                return ctx.canvas;
              }
            },
            getStream: function (ctx) {
              //获取画板流
              return function () {
                if (ctx.canDraw()) {
                  return ctx.makeStream();
                }
                return null;
              }
            },
            videoEnabled: function (ctx) {
              return function (flag) {
                ctx.hasShow = !!flag;
              }
            },
            videoFillEnabled: function (ctx) {
              return function (flag) {
                ctx.isFull = !!flag;
              }
            },
            isShare: function (ctx) {
              return function () {
                return !!ctx.cacheStream;
              }
            },
            share: function (ctx) {
              //分享白板
              return function (peer, flag) {
                if (_.isBoolean(peer)) {
                  flag = !!peer;
                }
                if (ctx.canDraw() && peer && peer.connectionState === 'connected') {
                  var localStream = yqWebrtcApp.Device.getStream();
                  ctx.cacheStream = localStream.clone();
                  var stream = ctx.makeStream(ctx.cacheStream);
                  ctx.senders = peer.getSenders();
                  ctx.senders.forEach(function (sender) {
                    stream.getTracks().forEach(function (track) {
                      if (sender.track.kind == track.kind) {
                        sender.replaceTrack(track).then(function () {}).catch(function (er) {
                          console.log(er);
                        });
                        if (flag) {
                          ctx.hasShow = flag;
                          //回显到自己的视图窗口
                          localStream.getVideoTracks().forEach(function (track) {
                            localStream.removeTrack(track);
                          });
                          localStream.addTrack(track, stream);
                        }
                      }
                    })
                  });
                  this.clear();
                }
              }
            },
            cancelShare: function (ctx) {
              //取消白板
              return function () {
                if (ctx.canDraw() && ctx.senders) {
                  var localStream = yqWebrtcApp.Device.getStream();
                  ctx.senders.forEach(function (sender) {
                    ctx.cacheStream.getTracks().forEach(function (track) {
                      if (sender.track.kind == track.kind) {
                        sender.replaceTrack(track).then(function () {}).catch(function (er) {
                          console.log(er);
                        });
                      }
                      if (ctx.hasShow) {
                        //回显到自己的视图窗口
                        localStream.getVideoTracks().forEach(function (track) {
                          localStream.removeTrack(track);
                        });
                        localStream.addTrack(track, ctx.cacheStream);
                      }
                    });
                  });
                  ctx.cacheStream = null;
                  ctx.hasShow = false;
                }
              }
            }
          }
        }),
        VideoMeeting = Define({
          /**
           * 视频会议
           */
          extends: {
            name: SenceBase,
            arguments: function (ctx) {
              //向父类传递参数
              return [ctx, 'videomeeting'];
            },
            super: function (ctx) {
              //父类执行后回调
              ctx.bindCommandEvent();
            }
          },
          inited: function (ctx) {
            return function (config, server) {
              _.extend(ctx.config, config);
              ctx.server = server;
              ctx.logger = server.getLogger()
            }
          },
          events: _C.VIDEOMEETING_EVENT,
          private: {
            config: {
              limit: 6, //限制人数
              oneWaitTime: 2, //会议只有一个人时等待时长自动结束会议
              mode: 'YUNQU_WEBRTC', //创建会议模式,YUNQU_WEBRTC自研 TRTC腾讯云
              isReconnect: false, //是否重连
              title: '', //会议标题
              desc: '' //会议描述
            },
            logger: null,
            creator: null, //创建者
            mainScreen: null, //当前主屏成员
            isInvited: false, //是否为被邀请 
            list: [], //会议成员列表
            peers: {},
            optional: {
              optional: [{
                DtlsSrtpKeyAgreement: true
              }]
            },
            bindCommandEvent: function (ctx) {
              //绑定指令事件
              return function () {
                var vThis = this;
                vThis.server.bindCommand(ctx, {
                  'resp_bind': function (e, isOk) {
                    if (isOk) {
                      if (!vThis.isInvited) {
                        ctx.init({
                          callId: e.confId
                        });
                      }
                      vThis._callback('waiting', vThis.formatReason(10005));
                      vThis._callback('ready', _.extend(vThis.formatReason(10013), {
                        msgData: {
                          confId: e.confId
                        }
                      }));
                      vThis.logger.log('获取会议成员列表...');
                      vThis.server.sendCommand('queryConfUser');
                      ctx.startHeart();
                      ctx.startTimer();
                    } else {
                      vThis._callback('fail', _.extend(vThis.formatReason(10006), {
                        msgData: e
                      }));
                    }
                  },
                  'resp_queryConfUser': function (e, isOk) {
                    if (isOk) {
                      vThis.creator = e.mainScreen;
                      var tList = ctx.getMemberList();
                      e.userList.forEach(function (user) {
                        var tIndex = tList.findIndex(function (item) {
                          return item.pbxUserId == user.pbxUserId;
                        });
                        if (tIndex == -1) {
                          vThis.addMember(vThis.formatMember(user));
                        }
                      });
                      vThis._callback('list', _.extend(vThis.formatReason(10012), {
                        msgData: {
                          list: tList,
                          mainScreen: e.mainScreen
                        }
                      }));
                      if (vThis.isInvited) {
                        vThis._callback('waiting', vThis.formatReason(10021));
                        Device.open().then(function (stream) {
                          vThis._callback('waiting', vThis.formatReason(10022));
                          vThis._callback('waiting', vThis.formatReason(10023));
                          tList.forEach(function (user) {
                            if (user.pbxUserId != vThis.server.getUserInfo().pbxUserId) {
                              vThis.createPeer(user, stream, false);
                            }
                          })
                        }).catch(function (e) {
                          vThis._callback('fail', _.extend(vThis.formatReason(10001), {
                            msgData: {
                              type: e.name,
                              tip: e.message
                            }
                          }));
                        });
                      }
                    } else {
                      vThis._callback('fail', _.extend(vThis.formatReason(10001), {
                        msgData: e
                      }));
                    }
                  },
                  'resp_invite': function (e, isOk) {
                    if (isOk) {
                      e.userList.forEach(function (user, index) {
                        if (user.isInvite) {
                          vThis.addMember(vThis.formatMember(user));
                        }
                      });
                      vThis._callback('wating', _.extend(vThis.formatReason(10008), {
                        msgData: {
                          list: e.userList
                        }
                      }))
                    } else {
                      vThis._callback('fail', _.extend(vThis.formatReason(10009), {
                        msgData: e
                      }))
                    }
                  },
                  'evt_invite': function (e, isOk) {
                    if (isOk) {
                      ctx.init({
                        callId: e.confId
                      });
                      vThis.isInvited = true;
                      vThis.creator = e.creator;
                      vThis._callback('invite', _.extend(vThis.formatReason(10001), {
                        msgData: {
                          user: e.from,
                          creator: e.creator,
                          confId: e.confId,
                          title: e.title,
                          desc: e.memo || '',
                          createTime: e.createTime,
                          password: e.password
                        }
                      }));
                    }
                  },
                  'evt_answer_invite': function (e, isOk) {
                    if (isOk) {
                      if (e.isAgree == _C.STATE.fail) {
                        vThis._callback('cancel', _.extend(vThis.formatReason(10014), {
                          msgData: {
                            user: e.from
                          }
                        }));
                        vThis.removeMember(e.from);
                      }
                      if (e.isAgree == -2) {
                        vThis._callback('timeout', _.extend(vThis.formatReason(10015), {
                          msgData: {
                            user: e.from
                          }
                        }));
                        vThis.removeMember(e.from);
                      }
                    }
                  },
                  'evt_stop': function (e, isOk) {
                    if (isOk) {
                      vThis._callback('leave', _.extend(vThis.formatReason(10011), {
                        msgData: {
                          user: e.member,
                          code: e.code,
                          tip: e.tip
                        }
                      }));
                      vThis.removeMember(e.member);
                      if (e.member.userId == ctx.getFromUser().userId) {
                        ctx.destory();
                      }
                    }
                  },
                  'evt_user_joinconf': function (e, isOk) {
                    if (isOk) {
                      vThis._callback('join', _.extend(vThis.formatReason(10010), {
                        msgData: {
                          user: e.member
                        }
                      }));
                      vThis.addMember(vThis.formatMember(e.member));
                    }
                  },
                  'evt_cancel_invite': function (e) {
                    if (isOk) {
                      vThis._callback('cancel', _.extend(vThis.formatReason(10016), {
                        msgData: {
                          user: e.member
                        }
                      }));
                      vThis.removeMember(e.member);
                    }
                  },
                  'evt_set_main_screen': function (e) {
                    if (isOk) {
                      vThis._callback('mainScreen', _.extend(vThis.formatReason(10017), {
                        msgData: {
                          user: e.member
                        }
                      }));
                      vThis.mainScreen = e.member;
                      vThis.updateMember(e.member);
                    }
                  },
                  'evt_end': function (e, isOk) {
                    if (isOk) {
                      vThis._callback('end', vThis.formatReason(10018));
                      ctx.destory();
                    }
                  },
                  'resp_answer_invite': function (e, isOk) {
                    if (isOk) {
                      vThis.server.sendCommand('bind', {
                        confId: e.confId,
                        one2One: false,
                        reconnect: vThis.isReconnect
                      });
                    }
                  },
                  'offer_meeting': function (e, isOk) {
                    if (isOk) {
                      vThis.createPeer(e.from, Device.getStream(), e.data);
                    }
                  },
                  'resp_offer_meeting': function (e, isOk) {
                    if (isOk) {
                      if (vThis.isRefuse) {
                        vThis.isRefuse = false;
                        vThis._callback('end', _.extend(vThis.formatReason(10018), {
                          msgData: e
                        }));
                        ctx.destory();
                      } else {
                        ctx.startHeart();
                      }
                    } else {
                      vThis._callback('fail', _.extend(vThis.formatReason(10002), {
                        msgData: e
                      }));
                    }
                  },
                  'answer_meeting': function (e, isOk) {
                    if (isOk) {
                      var tUser = e.from;
                      vThis.peers[tUser.userId].remote(e.data);
                    }
                  },
                  'resp_answer_meeting': function (e, isOk) {
                    if (!isOk) {
                      vThis.failCallback(10, {
                        tip: e.tip
                      });
                    }
                  },
                  'candidate': function (e, isOk) {
                    if (isOk) {
                      var tUser = e.from;
                      vThis.peers[tUser.userId].candidate(e.ice);
                    }
                  },
                  'resp_candidate': function (e, isOk) {
                    if (!isOk) {
                      vThis.logger.warn('交换candidate失败');
                      vThis.failCallback(10, {
                        tip: e.tip
                      });
                    }
                  },
                  'resp_kick': function (e, isOk) {
                    if (!isOk) {
                      vThis.failCallback(10, {
                        user: e.member,
                        tip: e.tip
                      });
                      vThis.removeMember(e.member);
                    }
                  },
                  'resp_quit': function (e, isOk) {
                    if (!isOk) {
                      vThis.failCallback(10, {
                        tip: e.tip
                      });
                    }
                  },
                  'resp_set_main_screen': function (e, isOk) {
                    if (!isOk) {
                      vThis.failCallback(10, {
                        tip: e.tip
                      });
                    }
                  },
                  'resp_end': function (e, isOk) {
                    if (!isOk) {
                      vThis.failCallback(10, {
                        tip: e.tip
                      });
                    }
                    ctx.destory();
                  }
                })
              }
            },
            createPeer: function (ctx) {
              return function (user, stream, sdp) {
                var vThis = this;
                var tPeer = ctx.createPipe(user.userId, stream, vThis.optional);
                tPeer._postData({
                  rtcAccountID: user.rtcAccountID,
                  userId: user.userId
                });
                tPeer.onReady(function (e) {
                  vThis._callback('waiting', vThis.formatReason(10024));
                  if (sdp) this.answer(sdp);
                  else this.offer();
                }).onConnect(function (e) {
                  var tArr = [{
                    type: 'local',
                    user: ctx.getFromUser(),
                    stream: stream
                  }];
                  _.each(vThis.peers, function (name, peer) {
                    tArr.push({
                      type: 'remote',
                      user: peer._postData(),
                      stream: peer.getRemoteStream()
                    })
                  });
                  vThis._callback('connect', _.extend(vThis.formatReason(10014), {
                    msgData: {
                      list: tArr,
                      callType: stream.getVideoTracks().length>0?'video':'audio'
                    }
                  }));
                }).onStatus(function (e) {
                  vThis.logger.info('通道状态：' + _.toJson(e));
                }).onFail(function (e) {
                  vThis.logger.info('通道失败：' + _.toJson(e));
                  vThis._callback('fail', vThis.formatReason(10028));
                }).onOffer(function (e) {
                  vThis._callback('waiting', vThis.formatReason(10026));
                  vThis.server.sendCommand('offer_meeting', {
                    confId: ctx.getCallId(),
                    to: this._postData(),
                    from: ctx.getFromUser(),
                    data: e.msgData.sdp,
                    forceReconnect: false
                  });
                }).onAnswer(function (e) {
                  vThis._callback('waiting', vThis.formatReason(10026));
                  vThis.server.sendCommand('answer_meeting', {
                    confId: ctx.getCallId(),
                    to: this._postData(),
                    from: ctx.getFromUser(),
                    data: e.msgData.sdp
                  });
                }).onCandidate(function (e) {
                  vThis._callback('waiting', vThis.formatReason(10027));
                  vThis.server.sendCommand('candidate', {
                    confId: ctx.getCallId(),
                    to: this._postData(),
                    from: ctx.getFromUser(),
                    ice: e.msgData.candidate
                  });
                });
                vThis.peers[user.userId] = tPeer;
                return tPeer;
              }
            },
            formatReason: function (ctx) {
              //格式化事件数据
              return function (code, reason) {
                return _U.callbackMsg(code, 'AudioCall', 'audiocall');
              }
            },
            formatMember: function (ctx) {
              //格式化成员
              return function (user) {
                var tUser = {
                  pbxUserId: user.pbxUserId,
                  isCreate: !!user.isCreate
                };
                if (user.rtcAccountID) tUser.rtcAccountID = user.rtcAccountID;
                if (user.userName) tUser.userName = user.userName;
                if (user.terminateType) tUser.terminateType = user.terminateType;
                if (user.status) {
                  tUser.status = user.status;
                  tUser.isFree = user.status == _C.STATE.free;
                }
                if (user.userId) tUser.userId = user.userId;
                if (user.video == undefined) tUser.video = true;
                else tUser.video = user.video == 1;
                if (user.audio == undefined) tUser.audio = true;
                else tUser.audio = user.audio == 1;
                return tUser;
              }
            },
            failCallback: function (ctx) {
              //请求失败回调
              return function (code, data) {
                this._callback('fail', _.extend(this.formatReason(code), {
                  msgData: data
                }))
              }
            },
            create: function (ctx) {
              //创建会议
              return function (title, desc) {
                var vThis = this;
                vThis.title = title,
                  vThis.desc = desc;
                vThis.server.requestCommand('createConf', {
                  title: title,
                  memo: desc,
                  sessionId: vThis.server.getTokenInfo().sessionId,
                  userId: vThis.server.getUserInfo().userId,
                  one_person_waiting_inverval: vThis.config.oneWaitTime
                }).then(function (e) {
                  vThis._callback('waiting', vThis.formatReason(10002));
                  vThis.logger.warn('创建会议成功');
                  vThis.logger.log('开始绑定会议...');
                  var tSerialId = vThis.server.sendCommand('bind', {
                    confId: e.confId,
                    one2One: false,
                    reconnect: vThis.isReconnect
                  });
                  ctx.setSerialId(tSerialId);
                }).catch(function (e) {
                  vThis._callback('fail', _.extend(vThis.formatReason(10003), {
                    msgData: {
                      type: e.name,
                      tip: e.message
                    }
                  }));
                })
              }
            },
            addMember: function (ctx) {
              //添加成员
              return function (user) {
                this.list.push(user);
                this.list = this.list.sort(function (a, b) {
                  return a.sort - b.sort;
                })
              }
            },
            removeMember: function (ctx) {
              //移除成员
              return function (user) {
                var tIndex = this.list.findIndex(function (item) {
                  return item.pbxUserId == user.pbxUserId;
                });
                if (tIndex != -1) this.list.splice(tIndex, 1);
              }
            },
            updateMember: function (ctx) {
              //更新成员
              return function (user) {
                var tUser = this.list.find(function (item) {
                  return user.pbxUserId == item.pbxUserId;
                });
                _.extend(tUser, this.formatMember(user));
              }
            }
          },
          public: {
            create: function (ctx) {
              //创建会议号
              return function (title, desc) {
                if (!title) {
                  ctx._callback('fail', ctx.formatReason(10012));
                  return;
                }
                ctx._callback('waiting', ctx.formatReason(10001));
                if (this.isActive()) {
                  ctx.logger.warn('已在会议中');
                  ctx._callback('fail', ctx.formatReason(10012));
                } else {
                  ctx.create(title, desc || '');
                }
              }
            },
            invite: function (ctx) {
              //邀请成员
              return function (list) {
                this.check(function (e) {
                  if (_.isObject(list)) list = [list];
                  var tList = this.getMemberList();
                  var tLen = tList.length;
                  list = list.filter(function (user) {
                    return !_.inArray(function (item) {
                      return item.userId == user.userId;
                    }, tList);
                  });
                  list = list.map(function (user) {
                    return {
                      pbxUserId: user.pbxUserId,
                      userName: user.userName,
                      sort: ++tLen
                    };
                  });
                  ctx._callback('waiting', ctx.formatReason(10007));
                  ctx.server.sendCommand('invite', {
                    userList: list
                  });
                })
              }
            },
            join: function (ctx) {
              //加入会议
              return function () {
                this.check(function (e) {
                  ctx.server.sendCommand('answerInvite', {
                    isAgree: '000',
                    toUserId: ctx.creator.userId,
                    confId: this.getCallId()
                  })
                })
              }
            },
            quit: function (ctx) {
              //退出会议
              return function () {
                this.check(function (e) {
                  ctx.server.sendCommand('quit', {
                    confId: this.getCallId(),
                    sessionId: ctx.server.getSessionId()
                  });
                  this.destory();
                })
              }
            },
            end: function (ctx) {
              //结束会议
              return function () {
                this.check(function (e) {
                  ctx.server.sendCommand('end', {
                    confId: this.getCallId(),
                    sessionId: ctx.server.getSessionId()
                  });
                })
              }
            },
            getMemberList: function (ctx) {
              //获取会议成员列表
              return function () {
                return ctx.list;
              }
            },
            setMainScreen: function (ctx) {
              //设置主屏
              return function (user) {
                this.check(function (e) {
                  ctx.server.sendCommand('setMainScreen', {
                    userId: _.isString(user) ? user : user.userId
                  })
                })
              }
            },
            kick: function (ctx) {
              //踢成员
              return function (user) {
                this.check(function (e) {
                  ctx.server.sendCommand('kick', {
                    member: {
                      rtcAccountID: user.rtcAccountID,
                      userId: user.userId
                    }
                  })
                })
              }
            }
          }
        }),
        Player = Define({
          /**
           * 播放器
           */
          inited: function (ctx) {
            return function (config) {
              _.extend(ctx.config, config);
              ctx.audio = new Audio();
              ctx.audio.autoplay = true;
              ctx.audio.playsinline = 'playsinline';
              _.bindEvent(ctx.audio, 'ended', function () {
                if (ctx.config.loop) {
                  ctx.audio.play();
                }
              });
            }
          },
          events: _C.PEER_EVENT,
          static: {
            CALLIN: 1,
            CALLOUT: 2,
            CALLWAIT: 3
          },
          private: {
            config: {
              callin: '',
              callout: '',
              callwait: '',
              loop: false
            },
            audio: null
          },
          public: {
            play: function (ctx) {
              return function (url) {
                if (this.isPlay()) this.pause();
                if (_.isNumber(url)) {
                  if (url == Player.CALLIN) url = ctx.config.callin;
                  else if (url == Player.CALLOUT) url = ctx.config.callout;
                  else if (url == Player.CALLWAIT) url = ctx.config.callwait;
                }
                if (url) {
                  ctx.audio.src = url;
                }
              }
            },
            pause: function (ctx) {
              return function () {
                if (this.isPlay()) {
                  ctx.audio.pause();
                }
              }
            },
            isPlay: function (ctx) {
              return function () {
                return ctx.audio && (!ctx.audio.paused || !ctx.audio.ended);
              }
            }
          }
        }),
        PeerConnection = Define({
          /**
           *点对点通道
           */
          inited: function (ctx) {
            return function (config) {
              _.extend(ctx.config, config);
              
              ctx.logger = new Logger({
                log: ctx.config.log
              });
              ctx.postData = config.postData;
              ctx.create();
            }
          },
          events: _C.PEER_EVENT,
          private: {
            config: {
              option: {
                optional: [{
                  DtlsSrtpKeyAgreement: true
                }]
              },
              turnServer: {
                iceServers: []
              },
              log: false,
              iceRestart: true
            },
            logger: null,
            peer: null,
            localSdp: '',
            remoteSdp: '',
            localStream: null,
            remoteStream: null,
            defaultStreamChange: false,
            cacheCandidate: [],
            postData: {},
            isReconnect: false,
            isPeerReconnect: false,
            localCacheStream: null,
            remoteCacheStream: null,
            isDisconnect: false,
            deviceEvents: null,
            videoEnabled: false,
            audioEnabled: false,
            sessionDesc: function () {
              return window.mozRTCSessionDescription || window.webkitRTCSessionDescription || window.RTCSessionDescription;
            },
            candidate: function () {
              return window.mozRTCIceCandidate || window.RTCIceCandidate;
            },
            connect: function () {
              return window.RTCPeerConnection || window.webkitRTCPeerConnection;
            },
            formatReason: function (ctx) {
              //格式化事件数据
              return function (code, reason) {
                return _.extend(_U.callbackMsg(code, 'PeerConnect', 'peerconnect'), {
                  userId: this.config.userId,
                  postData: this.postData
                });
              }
            },
            checkNetwork: function (ctx) {
              return function (fn) {
                service.send('heart');
                checkTimeout = setTimeout(fn, 2000);
              }
            },
            bindPeerEvent: function (ctx) {
              return function (target) {
                var vThis = this;
                var checkStatus = function(e){
                  vThis.logger.warn('管道状态');
                  vThis.logger.warn('iceGatheringState:'+target.iceGatheringState);
                  vThis.logger.warn('signalingState:'+target.signalingState);
                  vThis.logger.warn('iceConnectionState:'+target.iceConnectionState);
                  vThis.logger.warn('connectionState:'+target.connectionState); //chrome 70不支持该状态
                  if(target.iceGatheringState=='complete'&&target.connectionState=='connected'){
                    if(vThis.remoteStream){
                      vThis.remoteStream.getTracks().forEach(function(track){
                        track.onmute = null;
                        track.onunmute = null;
                      });
                      vThis.remoteStream = null;
                    }
                    vThis.remoteStream = new MediaStream();
                    target.getReceivers().forEach(function(receiver){
                      vThis.bindTrackEvent(receiver.track);
                      vThis.remoteStream.addTrack(receiver.track);
                    });
                    target.getSenders().forEach(function(sender){
                      Device.getStream().getTracks().forEach(function(track){
                        if(sender.track && sender.track.kind==track.kind){
                          sender.replaceTrack(track);
                        }
                      });
                    });
                    vThis._callback('connect', _U.peerConnection(10005, {
                      connectionState: target.connectionState,
                      iceconnectionstate: target.iceConnectionState,
                      signalingState: target.signalingState,
                      stream: vThis.remoteStream
                    }));
                    vThis.isPeerReconnect = false;
                  }else if(target.connectionState =='disconnected'){
                    vThis.isDisconnect = true;
                    vThis.reset();
                    vThis._callback('disconnect', _U.peerConnection(10019));
                  }
                };
                _.each({
                  icecandidate: function (e) {
                    if (e.candidate) {
                      if (!vThis.remoteSdp) {
                        vThis.logger.warn('缓存candidate');
                        vThis.cacheCandidate.push(e.candidate);
                      } else {
                        vThis._callback('candidate', _U.peerConnection(10001, e.candidate))
                      }
                    }
                  },
                  icecandidateerror: function (e) {
                    console.warn('icecandidateerror', e);
                  },
                  connectionstatechange: function (e) {
                    var state = target.connectionState;
                    checkStatus(e);
                    vThis._callback('status', _U.peerConnection(10014, {
                      state: state,
                      type: e.type
                    }));
                  },
                  signalingstatechange: function (e) {
                    checkStatus(e);
                    vThis._callback('status', _U.peerConnection(10014, {
                      state: target.signalingState,
                      type: e.type
                    }));
                  },
                  iceconnectionstatechange: function (e) {
                    checkStatus(e);
                    vThis._callback('status', _U.peerConnection(10014, {
                      state: target.iceConnectionState,
                      type: e.type
                    }));
                  },
                  icegatheringstatechange: function (e) {
                    checkStatus(e);
                    vThis._callback('status', _U.peerConnection(10014, {
                      state: target.iceGatheringState,
                      type: e.type
                    }));
                  },
                  negotiationneeded: function (e) {
                    checkStatus(e);
                  },
                  datachannel: function (e) {

                  },
                  track: function (e) {
                   
                  },
                  addStream: function (e) {
                  },
                  removestream: function (e) {

                  }
                }, function (type, callback) {
                  _.bindEvent(target, type, callback);
                })
              }
            },
            create: function (ctx) {
              //创建通道对象
              return function (fn) {
                try{
                  var vThis = this;
                  var stream = vThis.config.stream;
                  vThis.isDisconnect = false;
                  vThis.config.turnServer = Object.assign({
                    
                  }, vThis.config.turnServer);
                  //vThis.config.turnServer.sdpSemantics = 'unified-plan'; //要求低版本用unified-plan格式
                  //vThis.config.turnServer.rtcpMuxPolicy = 'negotiate'; //negotiate、require
                  //vThis.config.option.optional.push({rtcpMuxPolicy: 'negotiate'}); 
                  vThis.logger.warn({
                    tip: '创建通道的turnServer和optional设置项',
                    turnServer: vThis.config.turnServer,
                    optional: vThis.config.option
                  });
                  vThis.peer = new vThis.connect(vThis.config.turnServer, vThis.config.option);
                  console.log('通道对象', vThis.peer);
                  console.log('通道配置项', vThis.peer.getConfiguration?vThis.peer.getConfiguration():'不支持');
                  vThis.bindPeerEvent(vThis.peer);
                  vThis.initStats();
                  ctx.setStream(stream);
                  Timer.thread().then(function () {
                    vThis._callback('ready', _U.peerConnection(10007, {
                      config: vThis.config
                    }));
                  });
                  vThis.bindDeviceEvent();
                }catch(e){
                  console.warn(e.message);
                }
              }
            },
            check: function (ctx) {
              //检查通道是否连接
              return function (fn) {
                if (!this.peer) {
                  this.create();
                } else {
                  //this.logger.warn('通道未创建');
                  //this._callback('fail', this.formatReason(10011));
                }
                return fn.call(ctx, this.peer);
              }
            },
            bindTrackEvent: function(ctx){
              return function(track){
                var vThis = this;
                track.onmute = function(){
                  if(this.kind=='video'&&vThis.videoEnabled){
                    vThis.logger.warn('远端画面卡住');
                  }
                };
              }
            },
            updateMediaState: function(ctx){
              return function(type, state){
                var vThis = this;
                var curStream = vThis.remoteStream;
                curStream = vThis.remoteStream;
                if(type=='video'){
                  if(curStream){
                    if(state){
                      vThis.logger.log('远端画面非静止');
                      if(vThis.remoteCacheStream && curStream){
                        curStream.getVideoTracks().forEach(function(track){
                          curStream.removeTrack(track);
                        });
                        vThis.remoteCacheStream.getVideoTracks().forEach(function(track){
                          curStream.addTrack(track, vThis.remoteCacheStream);
                        });
                        vThis.remoteCacheStream = null;
                      }
                    }else{
                      vThis.logger.log('远端画面静止');
                      var img = Device.getDefaultNoVideoImg();
                      var def = Device.getDefinition();
                      vThis.remoteCacheStream = new MediaStream();
                      Device.makeStream({
                        width: def.width,
                        height: def.height,
                        draw: function(ctx, cav){
                          cav.width = def.width;
                          cav.height = def.height;
                          if(img) ctx.drawImage(img, 0, 0, def.width, def.height);
                          if(vThis.isDisconnect) return false;
                        }
                      }).then(function(stream){
                        curStream.getVideoTracks().forEach(function(track){
                          vThis.remoteCacheStream.addTrack(track);
                          curStream.removeTrack(track);
                        });
                        stream.getVideoTracks().forEach(function(track){
                          curStream.addTrack(track, stream);
                        });
                      });
                    }
                  }
                }
              }
            },
            reset: function(ctx){
              return function(){
                this.isReconnect = false;
                this.isPeerReconnect = false;
                this.remoteSdp = '';
                this.localSdp = '';
                this.cacheCandidate = [];
                Device.unbind('status', this.deviceEvents);
              }
            },
            bindDeviceEvent: function(ctx){
              return function(){
                var vThis = this;
                var curStream = vThis.localStream;
                var cacheStream = null;
                var replace = function(stream){
                  if(!vThis.peer) return;
                  stream.getTracks().forEach(function (track) {
                    if (ctx.isConnect()) {
                      var tSender = vThis.peer.getSenders().find(function (sender) {
                        return sender.track && sender.track.kind == track.kind;
                      });
                      if(tSender){
                        tSender.replaceTrack(track).then(function (e) {
                          vThis.logger.info(track.kind+'替换媒体流成功');
                        }).catch(function (e) {
                          vThis.logger.warn(track.kind+'替换媒体流失败');
                          vThis._callback('fail', _U.peerConnection(10010));
                        });
                      }
                    }
                  })
                };
                var callback = function(e){
                  if(e.type=='video'){
                    if(e.status){
                      vThis.logger.log('本地画面非静止');
                      if(cacheStream && curStream){
                        curStream.getVideoTracks().forEach(function(track){
                          curStream.removeTrack(track);
                        });
                        cacheStream.getVideoTracks().forEach(function(track){
                          track.enabled = true;
                          curStream.addTrack(track, cacheStream);
                        });
                        replace(curStream);
                        cacheStream = null;
                      }
                    }else{
                      var img = Device.getDefaultNoVideoImg();
                      var def = Device.getDefinition();
                      cacheStream = new MediaStream();
                      vThis.logger.log('本地画面静止');
                      if(curStream){
                        Device.makeStream({
                          width: def.width,
                          height: def.height,
                          draw: function(ctx, cav){
                            cav.width = def.width;
                            cav.height = def.height;
                            if(img) ctx.drawImage(img, 0, 0, def.width, def.height);
                            if(vThis.isDisconnect) return false;
                          }
                        }).then(function(stream){
                          curStream.getVideoTracks().forEach(function(track){
                            cacheStream.addTrack(track);
                            curStream.removeTrack(track);
                          });
                          stream.getVideoTracks().forEach(function(track){
                            curStream.addTrack(track, stream);
                          });
                          replace(curStream);
                        });
                      }
                    }
                  }
                };
                var deviceEvents = {
                  status: callback
                };
                Device.bind(deviceEvents);
                vThis.deviceEvents = callback;
                curStream.getTracks().forEach(function(track){
                  callback({type: track.kind, status: track.enabled});
                });
              }
            },
            initStats: function(ctx){
              //初始化数据统计
              return function(){
                var vThis = this;
                var tm = null;
                var videoLastTime = 0;
                var audioLastTime = 0;
                var open = function(type){
                  if(type=='video'){
                    console.warn('远端关闭摄像头');
                    vThis.videoEnabled = false;
                    vThis._callback('videoState', _U.peerConnection(10017, {
                      state: false
                    }));
                  }else if(type=='audio'){
                    console.warn('远端关闭麦克风');
                    vThis.audioEnabled = false;
                    vThis._callback('audioState', _U.peerConnection(10018, {
                      state: false
                    }));
                  }
                  vThis.updateMediaState(type, false);
                };
                var close = function(type){
                  if(type=='video'){
                    console.warn('远端打开摄像头');
                    vThis.videoEnabled = true;
                    vThis._callback('videoState', _U.peerConnection(10017, {
                      state: true
                    }));
                  }else if(type=='audio'){
                    console.warn('远端打开麦克风');
                    vThis.audioEnabled = true;
                    vThis._callback('audioState', _U.peerConnection(10018, {
                      state: true
                    }));
                  }
                  vThis.updateMediaState(type, true);
                };
                var video = {
                  open: open,
                  close: close
                };
                var audio = {
                  open: open,
                  close: close
                };
                var loop = function(){
                  vThis.peer.getStats().then(function(stats){
                    stats.forEach(function (report) {
                      if (_.inArray(function (item) {
                          return item == report.type;
                        }, ['candidate-pair', 'inbound-rtp', 'outbound-rtp', 'track', 'transport'])) {
                          if(report.type=='inbound-rtp') {
                            if(report.lastPacketReceivedTimestamp){
                              //console.log('统计', report);
                              var flag = false;
                              if(report.kind=='video'){
                                flag = report.lastPacketReceivedTimestamp==videoLastTime&&videoLastTime!=0;
                                if(flag){
                                  if(video.open){
                                    video.open('video');
                                    video.open = null;
                                    video.close = close;
                                  }
                                }else{
                                  if(video.close){
                                    video.close('video');
                                    video.close = null;
                                    video.open = open;
                                  }
                                }
                                videoLastTime = report.lastPacketReceivedTimestamp;
                                
                              }else if(report.kind=='audio'){
                                // flag = report.lastPacketReceivedTimestamp==audioLastTime&&audioLastTime!=0;
                                // if(flag){
                                //   if(audio.open){
                                //     audio.open('audio');
                                //     audio.open = null;
                                //     audio.close = close;
                                //   }
                                // }else{
                                //   if(audio.close){
                                //     audio.close('audio');
                                //     audio.close = null;
                                //     audio.open = open;
                                //   }
                                // }
                                // audioLastTime = report.lastPacketReceivedTimestamp;
                              }
                            }
                          }
                        }
                    });
                  });
                  if(tm) clearTimeout(tm);
                  if(!vThis.isDisconnect) tm = setTimeout(loop, 500);
                };
                loop();
              }
            }
          },
          public: {
            offer: function (ctx) {
              //创建offer
              return function (flag) {
                ctx.cacheCandidate = [];
                ctx.check(function (peer) {
                  peer.createOffer({
                    iceRestart: ctx.config.iceRestart
                  }).then(function (sdp) {
                    ctx.logger.info('执行createOffer成功');
                    console.log(sdp);
                    if (_.isArray(function (item) {
                        return item == ctx.sence;
                      }, ['audiomeeting'])) {

                    }
                    if(flag){
                      //走磐石时要指定profile-level-id和帧率
                      sdp.sdp = sdp.sdp.replace(/profile-level-id=[0-9a-zA-Z]{6}?/g, 'profile-level-id=42C01E');
                      sdp.sdp = sdp.sdp.replace(/:(\d+) H264\/\d+/g, ':$1 H264/90000\na=framerate:15\na=framesize:$1 480-640');
                      console.log('调整profile-level-id', sdp);
                    }
                    ctx.localSdp = sdp;
                    peer.setLocalDescription(new ctx.sessionDesc(sdp));
                    ctx._callback('offer', _U.peerConnection(10012, {
                      sdp: sdp
                    }));
                  }).catch(function (e) {
                    ctx.logger.info('执行createOffer失败');
                    ctx._callback('fail', _U.peerConnection(10013, {
                      type: e.name,
                      tip: e.message
                    }));
                  })
                })
              }
            },
            answer: function (ctx) {
              //创建answer
              return function (sdp) {
                ctx.check(function (peer) {
                  ctx.remoteSdp = sdp;
                  this.remote(sdp, function () {
                    peer.createAnswer().then(function (sdp) {
                      ctx.logger.info('执行createAnswer成功', sdp);
                      console.log(sdp);
                      ctx.localSdp = sdp;
                      peer.setLocalDescription(new ctx.sessionDesc(sdp));
                      ctx._callback('answer', _U.peerConnection(10014, {
                        sdp: sdp
                      }));
                    }).catch(function (e) {
                      ctx.logger.info('执行createAnswer失败');
                      ctx._callback('fail', _U.peerConnection(10015, {
                        type: e.name,
                        tip: e.message
                      }));
                    })
                  })
                });
              }
            },
            remote: function (ctx) {
              //设置远程描述
              return function (sdp, fn) {
                ctx.check(function (peer) {
                var txt = sdp.sdp;
                console.log('before', txt, sdp);
                if(/a=msid:/.test(txt)||/a=mid:audio/.test(txt)){
                  txt = txt.replace(/a=ssrc(:|-group)[^\n\r]+/g,'').replace(/[\n\r]+/g, '\n');//replace(/a=msid-semantic:.+/g, '').replace(/a=msid:.+/g, '').
                }
                if(ctx.localSdp){
                  var vIndex = ctx.localSdp.sdp.indexOf('m=video'), aIndex = ctx.localSdp.sdp.indexOf('m=audio');
                  if(vIndex!=-1&&aIndex!=-1&&vIndex<aIndex){
                    //因IOS的产生的SDP顺序问题需要调整answer的顺序与offer的顺序一样
                    var arr = [];
                    arr.push(txt.substring(0, txt.indexOf('m=audio')));
                    arr.push(txt.substring(txt.indexOf('m=video')));
                    arr.push(txt.substring(txt.indexOf('m=audio'), txt.indexOf('m=video')));
                    txt = arr.join('');
                    console.log('顺序调整', arr);
                  }
                }           
                console.log('after', txt, sdp);
                sdp.sdp = txt;
                  peer.setRemoteDescription(new ctx.sessionDesc(sdp)).then(function () {
                    ctx.logger.info('执行setRemoteDescription成功');
                    if (ctx.remoteSdp) {
                      fn();
                    } else {
                      ctx.remoteSdp = sdp;
                      ctx.cacheCandidate.forEach(function (ice) {
                        ctx._callback('candidate', _U.peerConnection(10016, {
                          candidate: ice
                        }));
                      });
                      ctx.cacheCandidate = [];
                    }
                  }).catch(function (e) {
                    ctx.logger.info('执行setRemoteDescription失败');
                    ctx._callback('fail', _U.peerConnection(10017, {
                      type: e.name,
                      tip: e.message
                    }));
                  })
                })
              }
            },
            candidate: function (ctx) {
              //设置candidate
              return function (ice) {
                ctx.check(function (peer) {
                  peer.addIceCandidate(new ctx.candidate(ice)).then(function () {
                    ctx.logger.info('执行addIceCandidate成功');
                  }).catch(function (e) {
                    ctx.logger.info('执行addIceCandidate失败');
                    ctx._callback('fail', _U.peerConnection(10008, {
                      type: e.name,
                      tip: e.message
                    }));
                  });
                })
              }
            },
            isConnect: function (ctx) {
              //通道是否连接中
              return function () {
                return ctx.peer && ['connecting', 'connected'].indexOf(ctx.peer.connectionState) != -1;
              }
            },
            isPeerReconnect: function(ctx){
              return function(){
                return ctx.isPeerReconnect;
              }
            },
            isFree: function (ctx) {
              //通道是否空闲
              return function () {
                return ctx.peer && ctx.peer.connectionState == 'new';
              }
            },
            disconnect: function (ctx) {
              //中断通道
              return function (flag) {
                if (this.isFree() || this.isConnect()) {
                  ctx.peer.close();
                  console.warn('断开通道...', ctx.peer.connectionState);
                  if (ctx.localStream && flag != false) {
                    ctx.localStream.getTracks().forEach(function (track) {
                      track.stop();
                    });
                  }
                  ctx.isDisconnect = true;
                  ctx.reset();
                }
              }
            },
            reconnect: function(ctx){
              return function(){
                ctx.isReconnect = true;
                ctx.peer.close();
              }
            },
            getConnect: function (ctx) {
              //获取通道对象
              return function () {
                return ctx.peer;
              }
            },
            setStream: function (ctx) {
              //设置媒体流
              return function (stream) {
                var that = this;
                var replace = function(stream){
                  if(!ctx.peer) return;
                  stream.getTracks().forEach(function (track) {
                    if (that.isConnect()) {
                      var tSender = ctx.peer.getSenders().find(function (sender) {
                        return sender.track && sender.track.kind == track.kind;
                      });
                      if(tSender){
                        tSender.replaceTrack(track).then(function (e) {
                          ctx.logger.info(track.kind+'替换远程媒体流成功');
                        }).catch(function (e) {
                          ctx.logger.warn(track.kind+'替换远程媒体流失败');
                          ctx._callback('fail', _U.peerConnection(10010));
                        });
                      }
                    }
                  })
                };
                if (stream.active) {
                  if (ctx.localStream) {
                    ctx.defaultStreamChange = false;
                    replace(stream);
                  } else {
                    if (ctx.peer.addTrack) {
                      stream.getTracks().forEach(function (track) {
                        ctx.peer.addTrack(track, stream);
                      });
                    } else {
                      ctx.peer.addStream(stream);
                    }
                  }
                  ctx.localStream = stream;
                  var onDeviceChange = function (stream){
                    Device.unbind('change', onDeviceChange);
                    that.setStream(stream);
                  };
                  Device.unbind('change', onDeviceChange);
                  Device.bind('change', onDeviceChange);
                } else {
                  ctx.logger.warn('媒体流不可用');
                  ctx._callback('fail', _U.peerConnection(10009));
                }
              }
            },
            getRemoteStream: function (ctx) {
              //获取远程流
              return function () {
                return ctx.remoteStream;
              }
            },
            getLocalStream: function (ctx) {
              //获取本地流
              return function () {
                return ctx.localStream;
              }
            }
          }
        });
        var Device = (function (_D) {
          var DeviceManager = null;
          var vStream = null; //缓存本地媒体流对象
          var vCacheStream = null; //保存本地视频流
          var vScreenStream = null; //桌面流
          var isScreen = false; //是否桌面共享
          var isClose = false; //是否关闭
          var vDeviceInfo = null; //当前启动媒体设备信息
          var vCameraList = []; //缓存摄像头列表
          var vMicroList = []; //缓存麦克风列表
          var vSpeakerList = []; //缓存扬声器列表
          var vDeviceList = [];
          var vConstraints = { //媒体约束条件
            video: true,
            audio: true
          };
          var vDefinition = { //媒体清晰度
            video: {},
            audio: {}
          };
          var logger = new Logger();
          var vVideoEnabled = true;
          var vAudioEnabled = true;
          var vCameraEnabled = true;
          var vMikeEnabled = true;
          var deviceEvents = {};
          var insertTm = null;
          var cancelInsertPlay = true;
          var hasShowInsert = false;
          var insertStream = null;
          var insertPeer = null;
          var cacheVideo = null;
          var sharePeer = null;
          var shareShow = false;
          var isCancelShare = false;
          var shareTimeout = null;
          var vShareStream = null;
          var enabledDevice = true;
          var vCameraStatus = true;
          var vMikeStatus = true;
          var mediaLevel = 0;
          var mediaLevels = [{
            width: 480,
            height: 640,
            frameRate: 15
          },{
            width: 640,
            height: 480,
            frameRate: 15
          }, {
            width: 1280,
            height: 720,
            frameRate: 30
          }, {
            width: 1920,
            height: 1080,
            frameRate: 30
          }];
          var openMode = 'video'; //video视频通话 audio语音通话
          var deviceAuthorType = 'all';
          var defaultNoVideoImg = null; //默认无视频时展示画面
          var vImgMode = 'stretch';
          var fCheck = function (fn, flag) {
            //检查是否启动媒体
            return _.promise(function (resolve, reject) {
              if (vStream && vStream.active || flag) {
                fn.call(DeviceManager, resolve, reject);
              } else {
                if (flag) reject(new Error('未启动媒体'));
              }
            });
          };
          var fRect = function(mode, width, height){
            var conf = DeviceManager.getDefinition();
            var res = {
                width: width,
                height: height,
                x: 0,
                y: 0
            };
            switch(mode){
                case 'fitWidth': 
                //适合宽度
                res.width = conf.width;
                if(width>conf.width) res.height = height * (conf.width/width);
                else res.height = height * (width/conf.width);
                res.y = (conf.height - res.height)/2;
                break;
                case 'fitHeight': 
                //适合高度
                res.height = conf.height;
                if(height>conf.height) res.width = width * (conf.height/height);
                else res.width = width * (height/config.height);
                res.x = (conf.width - res.width)/2;
                break;
                case 'stretch': 
                //拉伸
                res.width = conf.width;
                res.height = conf.height;
                break;
                case 'fill':
                //平铺填充
                res.x = 0;
                res.y = 0;
                break;
                case 'fit':
                //适合
                if(width>height){
                    if(height>conf.height) {
                      res.height = conf.height;
                      res.width = width * (conf.height/height);
                      res.y = 0;
                    }else res.y = (conf.height-res.height)/2;
                    res.x = (conf.width - res.width)/2;
                }else{
                    if(width>conf.width) {
                      res.width = conf.width;
                      res.height = height * (conf.width/width);
                      res.x = 0;
                    }else res.x = (conf.width - res.width)/2;
                    res.y = (conf.height - res.height)/2;
                }
                break;
            }
            return res;
          };
          var fOpen = function (constraint, fn, er, flag) {
            //启动媒体
            var noCamera = constraint.video&&vCameraList.length==0;
            if (vStream) {
              logger.warn('关闭旧媒体流');
              fCloseStream(vStream);
              if(vCacheStream) fCloseStream(vCacheStream);
            }
            if(noCamera) {
              constraint.video = false;
              logger.warn('无摄像头');
            }
            if(_.isMobile(true)&&_.isIos()) constraint.audio = true;
            logger.warn('启动媒体约束条件：'+_.toJson(constraint));
            if(enabledDevice==false||(!vCameraEnabled&&!vMikeEnabled)){
              //禁用媒体设备
              var mediaStream = new MediaStream();
              var audioTrack = fMixAudioTrack();
              mediaStream.addTrack(audioTrack);
              if(constraint.video){
                DeviceManager.makeStream({
                  width: def.width,
                  height: def.height,
                  frameRate: def.frameRate,
                  background: '#333',
                  draw: function(ctx, cav){
                    ctx.fillStyle = 'white';
                    ctx.font = '16px 雅黑';
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillText('无摄像头', cav.width/2, cav.height/2);
                  }
                }).then(function(tstream){
                  tstream.getVideoTracks().forEach(function(track){
                    mediaStream.addTrack(track, tstream);
                  });
                  fn(mediaStream);
                  logger.log('无设备流');
                }).catch(function(e){
                  throw e.message;
                });
              }else {
                fn(mediaStream);
                logger.log('无设备流');
              }
            }else{
              fGetUserMedia(constraint).then(function(stream) {
                var inner = function(){
                  if (vStream) {
                    stream.getTracks().forEach(function(track){
                      vStream.addTrack(track);
                    });
                    fEmitEvent('change', stream);
                  }else {
                    fEmitEvent('open', stream);
                    vStream = stream;
                    vStream.oninactive = function(){
                      logger.log('stream inactive');
                    };
                    vStream.onactive = function(){
                      logger.log('stream active');
                    };
                    vStream.onended = function(){
                      logger.log('stream ended');
                    };
                  }
                  vCacheStream = new MediaStream();
                  vStream.getTracks().forEach(function(track){
                    vCacheStream.addTrack(track);
                  });
                  vDeviceInfo = fInitDevice(vStream);
                  logger.log('启动成功');
                  logger.warn('当前媒体设备信息：'+_.toJson(vDeviceInfo));
                  if(!vVideoEnabled||!DeviceManager.cameraStatus()) DeviceManager.turnOffCamera();
                  if(!vAudioEnabled||!DeviceManager.microStatus()) DeviceManager.turnOffMicro();
                  DeviceManager.setDefinition(mediaLevel);
                  fn(vStream);
                };
                if(noCamera||!vCameraEnabled){
                  //无摄像头
                  var def = DeviceManager.getDefinition();
                  var rect = defaultNoVideoImg?fRect(vImgMode, defaultNoVideoImg.width, defaultNoVideoImg.height):null;
                  DeviceManager.makeStream({
                    width: def.width,
                    height: def.height,
                    frameRate: def.frameRate,
                    background: '#333',
                    draw: function(ctx, cav){
                      if(defaultNoVideoImg&&rect){
                        ctx.fillStyle = '#3C99FF';
                        ctx.fillRect(0, 0, def.width, def.height);
                        ctx.drawImage(defaultNoVideoImg, rect.x, rect.y, rect.width, rect.height);
                      }else{
                        ctx.fillStyle = 'white';
                        ctx.font = '16px 雅黑';
                        ctx.textAlign = 'center';
                        ctx.textBaseline = 'middle';
                        ctx.fillText('无摄像头', cav.width/2, cav.height/2);
                      }
                    }
                  }).then(function(tstream){
                    tstream.getVideoTracks().forEach(function(track){
                      stream.addTrack(track, tstream);
                    });
                    inner();
                  }).catch(function(e){
                    throw e.message;
                  })
                }else inner();
              }).catch(function (e) {
                er(e);
                fEmitEvent('error', e);
              });
            }
            
          };
          var fBindEvent = function (events) {
            for (var key in events) {
              if (!deviceEvents[key]) deviceEvents[key] = [];
              deviceEvents[key].push({
                key: key,
                callback: events[key].callback,
                once: !!events[key].once
              });
            }
          };
          var fUnbindEvent = function () {
            var key = '';
            var callback = null;
            if (arguments.length > 0) {
              key = arguments[0];
              deviceEvents[key] = [];
            } else if (arguments.length > 1) {
              callback = arguments[1];
              deviceEvents[key] = deviceEvents[key].filter(function (item) {
                return item != callback;
              });
            }else if(arguments.length==0) deviceEvents = {};
            if (key && deviceEvents[key].length == 0) {
              if(key) delete deviceEvents[key];
            }
          };
          var fEmitEvent = function (key, data) {
            if (deviceEvents[key]) {
              var arr = [];
              deviceEvents[key].forEach(function (item) {
                if (item.callback) item.callback(data);
                if (!item.once) arr.push(item);
              });
              deviceEvents[key] = arr;
            }
          };
          var fInitDevice = function (stream) {
            var tInfo = {};
            if(stream){
              stream.getTracks().forEach(function(track){
                console.warn(track.kind+'自定义约束', track.getConstraints(), track.getSettings());
                var info = _.extend({
                  label: track.label,
                  id: track.id,
                  kind: track.kind
                }, track.getSettings(), {
                  capabilities: track.getCapabilities ? track.getCapabilities() : {}
                });
                if(track.kind=='video'){
                  tInfo.video = info;
                  vDefinition.video = {
                    width: tInfo.video.width,
                    height: tInfo.video.height,
                    frameRate: tInfo.video.frameRate
                  };
                  //前置摄像头
                  tInfo.front = /front|前置/i.test(track.label);
                  //后置摄像头
                  tInfo.back = /back|后置/i.test(track.label);
                }else if(track.kind=='audio'){
                  tInfo.audio = info;
                  vDefinition.audio = {
                    sampleRate: tInfo.audio.sampleRate
                  };
                  //听筒
                  tInfo.ear = /earpiece|听筒/i.test(track.label);
                  //外放
                  tInfo.speak = /speaker|默认|default/i.test(track.label)||track.label=='';
                }
                fEmitEvent('status', {type: track.kind, status: track.enabled});
              });
            }
            return tInfo;
          };
          var fReplaceStream = function(peer, stream){
            //替换轨道
            if(peer && peer.connectionState != 'closed' && stream){
              peer.getSenders().forEach(function (sender) {
                stream.getTracks().forEach(function (track) {
                  if (track.kind == sender.track.kind) {
                    sender.replaceTrack(track).then(function () {
                      console.warn(track.kind+'轨道替换成功');
                    }).catch(function (er) {
                      console.error(track.kind+'轨道替换失败：'+er.message);
                    });
                  }
                })
              })
            }
          };
          var fDeviceChange = function () {
            setTimeout(function () {
              fDeviceHasChange();
              fDeviceChange();
            }, 300);
          };
          var fDeviceHasChange = function () {
            var olist = vDeviceList;
            logger.log('媒体发生变化');
            fDeviceList(function (list) {
              var less  = list.length-olist.length;
              if(less>0){
                //插入
                list.forEach(function(device){
                  if(olist.find(function(item){device.deviceId==item.deviceId})==null){
                    logger.log('新设备插入:'+device.label);
                    fEmitEvent('insert', device);
                  }
                });
              }else if(less<0){
                //拨出
                olist.find(function(device){
                  if(list.find(function(item){device.deviceId==item.deviceId})==null){
                    logger.log('设备拨出:'+device.label);
                    fEmitEvent('pull', device);
                  }
                });
              }
            })
          };
          var fDeviceList = function (fn) {
            if (_D.mediaDevices.enumerateDevices) {
              var deviceList = [];
              var outputCount = 0;
              var loopDeviceCheck = function(list, fn){
                var device = list[0];
                list = list.slice(1);
                if(device){
                  var constraint = null;
                  if(device.kind=='videoinput'&&deviceAuthorType!='audio') constraint = {video: {deviceId: device.deviceId}};
                  else if(device.kind=='audioinput'&&deviceAuthorType!='video') constraint = {audio: {deviceId: device.deviceId}};
                  else if(device.kind=='audiooutput') {
                    deviceList.push({
                      deviceId: device.deviceId,
                      label: device.label||'扬声器'+(++outputCount),
                      kind: device.kind,
                      groupId: device.groupId
                    });
                  }
                  if(constraint&&!/IR/i.test(device.label)&&(constraint.video&&vCameraEnabled||constraint.audio&&vMikeEnabled)){
                    fGetUserMedia(constraint).then(function(stream){
                      var track = stream.getTracks()[0];
                      deviceList.push(getDeviceInfo(device, track));
                      fCloseStream(stream);
                      loopDeviceCheck(list, fn);
                    }).catch(function(e){
                      logger.error('检测媒体异常:'+e.message+':'+e.name);
                      loopDeviceCheck(list, fn);
                    });
                  }else loopDeviceCheck(list, fn);
                }else if(fn) fn(deviceList);
              };
              var getDeviceInfo = function(device, track){
                var kind = device.kind;
                return {
                  deviceId: device.deviceId,
                  label: track.label||(device.deviceId=='default'?'默认':''),
                  kind: kind,
                  groupId: device.groupId
                };
              };
              var initList = function(list){
                logger.warn('格式化后媒体列表：'+_.toJson(list));
                vDeviceList = list;
                vCameraList = [];
                vMicroList = [];
                vSpeakerList = [];
                list.forEach(function (info) {
                  if (info.kind === 'videoinput') {
                    vCameraList.push(info);
                  }else if (info.kind === 'audioinput') {
                    vMicroList.push(info);
                  }else if (info.kind === 'audiooutput') vSpeakerList.push(info);
                });
                fEmitEvent('list', list);
                if (fn) fn(list);
              };
              _D.mediaDevices.enumerateDevices().then(function (list) {
                logger.log('原媒体列表:'+_.toJson(list.map(function(device){ return {
                  label: device.label,
                  deviceId: device.deviceId,
                  groupId: device.groupId,
                  kind: device.kind
                }})));
                loopDeviceCheck(list.slice(0), initList);
                // if(loop){
                //   loopDeviceCheck(list.slice(0), initList);
                // }else {
                //   initList(list);
                // }
              }).catch(function(e){
                logger.error('监听媒体异常:'+e.message+':'+e.name);
                fEmitEvent('error', e);
              });
            }
          };
          var fCloseStream = function(stream){
            if(stream){
              stream.getTracks().forEach(function(track){
                track.stop();
                stream.removeTrack(track);
              });
            }
          };
          var fStartShare = function(peer, stream, show){
            if(stream){
              stream.addEventListener('inactive', function(){
                console.warn('分享资源流关闭');
                if(DeviceManager.isShare()) DeviceManager.cancelShare();
              });
              vShareStream = stream;
              if(show){
                //回显本地画面
                stream.getTracks().forEach(function(track){
                  vStream.getTracks().forEach(function (item) {
                    if (track.kind == item.kind) {
                      vStream.removeTrack(item);
                      vStream.addTrack(track, stream);
                    }
                  });
                });
              }
              if(peer){
                sharePeer = peer;
                fReplaceStream(peer, stream);
              }
            }
          };
          var fResetLocalStream = function(){
            if(vCacheStream && vCacheStream.active){
              vCacheStream.getTracks().forEach(function(track){
                vStream.getTracks().forEach(function(item){
                  if(track.kind==item.kind){
                    console.warn(item.kind+'本地媒体轨道替换成功');
                    vStream.removeTrack(item);
                    vStream.addTrack(track, vCacheStream);
                  }
                });
              });
              if(DeviceManager.isShare()){
                fReplaceStream(sharePeer, vCacheStream, true);
              }
            }
          };
          var fMakeStream = function (node, ok, fail) {
            var def = DeviceManager.getDefinition();
            var curLevel = mediaLevel;
            var width = node.width||node.offsetWidth||node.videoWidth||def.width;
            var height = node.height || node.offsetHeight || node.videoHeight||def.height;
            var isVideo = node instanceof HTMLVideoElement;
            DeviceManager.makeStream({
              width: def.width||width,
              height: def.height||height,
              draw: function(ctx, cav){
                if(curLevel!=mediaLevel){
                  def = Device.getDefinition();
                  cav.width = def.width;
                  cav.height = def.height;
                }
                if(width>height){
                  if(width>def.width) {
                    height = height*(def.width/width);
                    width = def.width;
                  }
                }else{
                  if(height>def.height){
                    width = width*(def.height/height);
                    height = def.height;
                  }
                }
                var x = (def.width-width)/2;
                var y = (def.height-height)/2;
                ctx.drawImage(node, x, y, width, height);
              },
              end: function(){
                if(isVideo && !node.paused) node.pause();
              }
            }).then(ok).catch(fail);
          };
          var fMixAudioTrack = function(tracks){
            var ac = new AudioContext();
            var dest = ac.createMediaStreamDestination();
            if(_.isArray(tracks)){
              tracks.forEach(function(track){
                var stream = new MediaStream();
                stream.addTrack(track);
                var mss = ac.createMediaStreamSource(stream);
                mss.connect(dest);
              });
            }else if(!tracks){
              var source = ac.createBufferSource();
              var frameCount = ac.sampleRate*2.0;
              var buffer = ac.createBuffer(2, frameCount, ac.sampleRate);
              for(var i=0; i<2; i++){
                for(var j=0; j<frameCount; j++){
                  buffer[j] = Math.random()*2-1;
                }
              }
              source.buffer = buffer;
              source.connect(dest);
              source.loop = true;
              source.start();
            }
            dest.context.resume();
            return dest.stream.getAudioTracks()[0];
          };
          var fCheckWebkitVersion = function(stream){
            var flag = true;
            stream.getTracks().forEach(function(track){
              if(!track.getSettings) flag = false;
            });
            var connection = window.RTCPeerConnection || window.webkitRTCPeerConnection;
            var peer = new connection();
            if(peer.addTrack){
              stream.getTracks().forEach(function(track){
                peer.addTrack(track, stream);
              })
            }else{
              peer.addStream(stream);
            }
            if(!peer.getConfiguration) flag=false;
            else{
              var config = peer.getConfiguration();
              if(config.sdpSemantics&&config.sdpSemantics=='plan-b'){
                flag=false;
              }
            }
            if(!flag) console.warn('内核版本过低');
            return flag;
          };
          var fGetUserMedia = function(constraint){
            if(!vCameraEnabled) constraint.video = false;
            if(!vMikeEnabled) constraint.audio = false;
            return _D.mediaDevices.getUserMedia(constraint);
          };
          if (_D.isSupport) {
            //监听媒体设备变化
            if (_D.mediaDevices.ondevicechange == null) {
              var tm = null;
              _.bindEvent(_D.mediaDevices, 'devicechange', function(){
                console.warn('媒体设备发生变化...');
                if(tm) clearTimeout(tm);
                tm = setTimeout(fDeviceHasChange, 1000);
              });
            } else {
              //部分浏览器不支持ondevicechange回调方法，估通过300毫秒定时器检查媒体列表变化实现该回调方法
              fDeviceChange();
            }
          }
          DeviceManager = {
            setVideoEnabled: function(flag){
              if(_.isBoolean(flag)) vVideoEnabled = flag;
            },
            setAudioEnabled: function(flag){
              if(_.isBoolean(flag)) vAudioEnabled = flag;
            },
            setCameraEnabled: function(flag){
              if(_.isBoolean(flag)) vCameraEnabled = flag;
            },
            setMikeEnabled: function(flag){
              if(_.isBoolean(flag)) vMikeEnabled = flag;
            },
            setDefaultNoVideoImg: function(url, mode){
              if(url){
                if(/fitWidth|fitHeight|fit|fill|stretch/i.test(mode)) vImgMode = mode;
                defaultNoVideoImg = new Image();
                defaultNoVideoImg.src = url;
                defaultNoVideoImg.crossOrigin = 'Anonymous';
                defaultNoVideoImg.onerror = function(){
                  defaultNoVideoImg = null;
                };
              }
            },
            getDefaultNoVideoImg: function(){
              return defaultNoVideoImg;
            },
            check: function(kind){
              return _.promise(function(ok, fail){
                if(this.isSupport()){
                  var options = {video:true, audio: true};
                  if(/audio|video|all/.test(kind)) {
                    deviceAuthorType = kind;
                    if(kind=='audio') {
                      options.video = false;
                    }else if(kind=='video') {
                      options.audio = false;
                    }
                  }
                  fGetUserMedia(options).then(function(stream){
                    if(!fCheckWebkitVersion(stream)) {
                      var er = {message: '内核版本过低'};
                      fEmitEvent('fail', er);
                      fail(er);
                      return;
                    }
                    stream.getTracks().forEach(function(track){
                      var sets = track.getSettings?track.getSettings():null;
                      if(sets){
                        if(track.kind=='video') vConstraints.video = {deviceId: sets.deviceId};
                        else if(track.kind=='audio') vConstraints.audio = {deviceId: sets.deviceId};
                      }
                    });
                    logger.log('检查媒体：'+_.toJson(vConstraints));
                    fCloseStream(stream);
                    fDeviceList(function(){
                      fEmitEvent('ready');
                    });
                    ok();
                  }).catch(function(e){
                    fEmitEvent('fail', e);
                    fail(e);
                  })
                }else {
                  var er = {message: '不支持webrtc'};
                  fEmitEvent('fail', er);
                  fail(er);
                }
              }.bind(this));
            },
            enabledDevice: function(flag){
              //启用或禁用媒体流，禁用时会模拟音频和视频流
              enabledDevice = flag;
            },
            isSupport: function () {
              //是否支持使用硬件媒体功能
              return _D.isSupport;
            },
            getCameraList: function () {
              //获取摄像头列表
              return vCameraList;
            },
            getMicroList: function () {
              //获取麦克风列表
              return vMicroList;
            },
            getSpeakerList: function () {
              //获取扬声器列表
              return vSpeakerList;
            },
            getStream: function () {
              //获取本地媒体流对象
              return vStream;
            },
            getDeviceInfo: function () {
              //获取当前启动媒体设备信息
              return vDeviceInfo;
            },
            getValidDevice: function () {
              //获取有效媒体设备
              var tCameraList = this.getCameraList();
              var tMicroList = this.getMicroList();
              var tDeviceInfo = {};
              if (tCameraList.length > 0) {
                tDeviceInfo.video = tCameraList[0];
              }
              if (tMicroList.length > 0) {
                tDeviceInfo.audio = tMicroList[0];
              }
              return tCameraList.length == 0 && tMicroList.length == 0 ? null : tDeviceInfo;
            },
            setMediaConstraint: function (constraints, ok, fail) {
              //设置媒体约束，在启动媒体前调用
              _.extend(vConstraints, constraints);
              if (vStream) {
                var tFlag = false;
                if (vStream.active) {
                  vStream.getTracks().forEach(function (track) {
                    if (track.applyConstraints && vConstraints[track.kind]) {
                      tFlag = true;
                      track.applyConstraints(vConstraints[track.kind]).then(function (e) {
                        if (ok) ok(e);
                      }).catch(function (e) {
                        if (fail) fail(e);
                      });
                    } else {
                      track.stop();
                    }
                  });
                }
                if (!tFlag) {
                  fGetUserMedia(vConstraints).then(function (stream) {
                    vStream = stream;
                    if (ok) ok(stream);
                  }).catch(function (e) {
                    if (fail) fail(e);
                  });
                }
              }
            },
            getMediaConstraint: function () {
              //获取当前媒体约束
              return vConstraints;
            },
            setDefinition: function (level) {
              //设置清晰度，在启动媒体前调用 definition=1==480|2==720|3==1080|4==1920
              return new Promise(function(resolve, reject){
                if(this.isOpen()){
                  var stream = this.getStream();
                  var track = stream.getVideoTracks();
                  if(track.length>0) {
                    track = track[0];
                    if(_.isNumber(level) && level>=0&&level<mediaLevels.length){
                      mediaLevel = level;
                      console.warn('设置清晰度', mediaLevels[level]);
                      track.applyConstraints(mediaLevels[level]).then(function(){
                        console.warn('设置清晰度成功');
                        resolve();
                      }).catch(function(e){
                        console.warn('设置清晰度失败');
                        reject(e);
                      });
                    }
                  }
                }else reject('未启动媒体');
              }.bind(this));
            },
            getDefinition: function (level) {
              //返回当前清晰度
              return mediaLevels[_.isNumber(level) && level>=0&&level<mediaLevels.length?level:mediaLevel];
            },
            open: function (mode, reOpen) {
              //打开媒体
              return fCheck(function (ok, fail) {
                vShareStream = null;
                sharePeer = null;
                isClose = false;
                openMode = mode==false?'audio':'video';
                if (this.isOpen()&&!reOpen) {
                  console.warn('media had opened');
                  ok(this.getStream());
                } else {
                  var cons = this.getMediaConstraint();
                  if(_.isMobile()&&!_.isIos()){
                    var index = vMicroList.findIndex(function(device){
                      return /speaker|扬声/i.test(device.label);
                    });
                    if(index!=-1) {
                      if(_.isBoolean(cons.audio)) cons.audio = {deviceId: vMicroList[index].deviceId};
                      else cons.audio.deviceId = vMicroList[index].deviceId;
                    }
                  }
                  if(mode==false){
                    cons.video = false;
                  }else {
                    cons.video = true;
                  }
                  fOpen(cons, function (stream) {
                    isClose = false;
                    ok(stream);
                  }, function (e) {
                    fail(e);
                  }, _.isMobile());
                }
              }, true)
            },
            close: function () {
              //关闭媒体
              return _.promise(function(ok){
                if(vStream) console.warn('关闭媒体');
                isClose = true;
                fCloseStream(vStream);
                fCloseStream(vCacheStream);
                fCloseStream(vShareStream);
                vCacheStream = null;
                vStream = null;
                vShareStream = null;
                vCameraStatus = true;
                vMikeStatus = true;
                ok();
                fEmitEvent('close');
              }.bind(this));
            },
            makeStream: function(options){
              //生成媒体流
              return _.promise(function(ok){
                var cav = document.createElement('canvas');
                var ctx = cav.getContext('2d');
                var stream = null;
                var tm = null;
                var draw = function () {
                  if(isClose) return;
                  ctx.clearRect(0, 0, cav.width, cav.height);
                  ctx.fillStyle = config.background;
                  ctx.fillRect(0, 0, cav.width, cav.height);
                  ctx.save();
                  if(_.isFunction(config.draw)) {
                    if(config.draw(ctx, cav)==false) {
                      if(_.isFunction(config.end)) config.end();
                      return;
                    }
                  }
                  ctx.restore();
                  if(tm) clearTimeout(tm);
                  tm = setTimeout(draw);
                };
                var config = Object.assign({
                  frameRate: 32,
                  width: 200,
                  height: 200,
                  background: 'black',
                  draw: function(){},
                  end: function(){}
                }, options);
                cav.width = config.width;
                cav.height = config.height;
                draw();
                stream = cav.captureStream(config.frameRate);
                stream.oninactive = function(){
                  if(tm) clearTimeout(tm);
                  if(_.isFunction(config.end)) config.end();
                  tm = null;
                };
                ok(stream);
              });
            },
            cameraStatus: function(){
              //摄像头状态
              if(this.isOpen()){
                return vCameraStatus;
              }
              return false;
            },
            microStatus: function(){
              //麦克风状态
              if(this.isOpen()){
                return vMikeStatus;
              }
              return false;
            },
            turnOffCamera: function () {
              //关闭摄像头
              return fCheck(function (ok, fail) {
                try {
                  this.getStream().getVideoTracks().forEach(function (track) {
                    vCameraStatus = false;
                    track.enabled = false;
                    track.dispatchEvent(new Event('mute', { bubbles: true, cancelable: false }));
                    fEmitEvent('status', {type: track.kind, status: track.enabled});
                  });
                  ok();
                } catch (e) {
                  fail(e);
                }
              })
            },
            turnOnCamera: function () {
              //打开摄像头
              return fCheck(function (ok, fail) {
                try {
                  this.getStream().getVideoTracks().forEach(function (track) {
                    vCameraStatus = true;
                    track.enabled = true;
                    track.dispatchEvent(new Event('unmute', { bubbles: true, cancelable: false }));
                    fEmitEvent('status', {type: track.kind, status: track.enabled});
                  });
                  ok();
                } catch (e) {
                  fail(e);
                }
              })
            },
            turnOffMicro: function () {
              //关闭麦克风
              return fCheck(function (ok, fail) {
                try {
                  this.getStream().getAudioTracks().forEach(function (track) {
                    vMikeStatus = false;
                    track.enabled = false;
                    track.dispatchEvent(new Event('mute', { bubbles: true, cancelable: false }));
                    fEmitEvent('status', {type: track.kind, status: track.enabled});
                  });
                  ok();
                } catch (e) {
                  fail(e);
                }
              })
            },
            turnOnMicro: function () {
              //打开麦克风
              return fCheck(function (ok, fail) {
                try {
                  this.getStream().getAudioTracks().forEach(function (track) {
                    vMikeStatus = true;
                    track.enabled = true;
                    track.dispatchEvent(new Event('unmute', { bubbles: true, cancelable: false }));
                    fEmitEvent('status', {type: track.kind, status: track.enabled});
                  });
                  ok();
                } catch (e) {
                  fail(e);
                }
              })
            },
            switchCamera: function () {
              //切换摄像头
              return fCheck(function (ok, fail) {
                var list = vCameraList.filter(function (device) {
                  /**
                   * 排除红外摄像头
                   */
                  return !/IR/i.test(device.label);
                });
                if(this.canSwitchCamera()&&list.length>1){
                  var constraint = this.getMediaConstraint();
                  var index = list.findIndex(function (device) {
                    return device.deviceId === vDeviceInfo.video.deviceId;
                  });
                  if(_.isMobile()){
                    var type = this.isFrontMode()?'environment':'user';
                    if(_.isBoolean(constraint.video))  constraint.video = {facingMode: type};
                    else constraint.video.facingMode=type;
                  }else{
                    if (index !== -1) {
                      index += 1;
                      if (index >= list.length) index = 0;
                      var id = list[index].deviceId;
                      if(_.isBoolean(constraint.video)) constraint.video = {deviceId:id};
                      else constraint.video.deviceId = id;
                    }
                  }
                  fOpen(constraint, ok, fail, true);
                }else {
                  fail('不可切换');
                }
              })
            },
            switchMicro: function () {
              //切换麦克风
              return fCheck(function (ok, fail) {
                if(this.canSwitchMicro()){
                  var deviceId = '';
                  var constraint = this.getMediaConstraint();
                  var label = _.isMobile()&&!_.isIos()?this.isEarMode()?/speaker|扬声/i:/earpiece|听筒/i:'';
                  var index = vMicroList.findIndex(function (device) {
                    if(label){
                      return label.test(device.label);
                    }else return device.deviceId === vDeviceInfo.audio.deviceId;
                  });
                  if(_.isMobile()&&!_.isIos()){
                    deviceId = vMicroList[index].deviceId;
                  }else{
                    if(index==-1&&vMicroList.length>1) index=0;
                    if (index != -1){
                      index += 1;
                      if (index >= vMicroList.length) index = 0;
                      deviceId = vMicroList[index].deviceId;
                    }
                  }
                  if(deviceId){
                    if(_.isBoolean(constraint.audio)) constraint.audio = {deviceId:deviceId};
                    else constraint.audio.deviceId = deviceId;
                    fOpen(constraint, ok, fail, true);
                  }
                }else {
                  fail('不可切换');
                }
              })
            },
            canSwitchMicro: function(){
              //可否切换麦克风，ios可能不支持切换
              return this.getMicroList().length>1;
            },
            canSwitchCamera: function(){
              //可否切换摄像头
              return this.getCameraList().length>1;
            },
            canShare: function () {
              //是否可以分享及白板
              return this.isOpen() && vStream && vStream.getVideoTracks().length > 0;
            },
            isShare: function(){
              //是否正在分享
              return sharePeer && vShareStream;
            },
            isEarMode: function(){
              //是否听筒模式
              return vStream && vStream.getAudioTracks().length>0&&/earpiece|听筒/i.test(vStream.getAudioTracks()[0].label);
            },
            isFrontMode: function(){
              //是否为前置摄像头
              return vStream && vStream.getVideoTracks().length>0&&/front|前置/i.test(vStream.getVideoTracks()[0].label);
            },
            getMode: function(){
              return openMode;
            },
            shareScreen: function (peer, show) {
              //桌面分享
              return fCheck(function (ok, fail) {
                _D.mediaDevices.getDisplayMedia({video: true, audio: false}).then(function(stream) {
                  fStartShare(peer, stream, show);
                  ok(stream);
                }).catch(fail);
              }, true);
            },
            getShareStream: function () {
              return vShareStream;
            },
            takePhoto: function (stream, flag) {
              //拍照
              return fCheck(function (ok, fail) {
                var vThis = this;
                var video = document.createElement('video');
                stream = stream || vStream;
                if(stream.getVideoTracks().length>0){
                  video.srcObject = stream;
                  video.autoplay = true;
                  video.muted = true;
                  video.oncanplay = function(){
                    video.play();
                    vThis.makeStream({
                      width: this.videoWidth,
                      height: this.videoHeight,
                      draw: function(ctx, cav){
                        ctx.drawImage(video, 0, 0);
                        var url = cav.toDataURL();
                        if(flag){
                          //下载
                          _.downloadResource(url, 'screenshot_'+_.datetime().format('yyyyMMddhhmmss')+'.png').then(ok).catch(fail);
                        }else ok(url);
                        return false;
                      }
                    })
                  };
                  video.onerror = fail;
                }else fail('无视频流');
              });
            },
            switchShareSound: function(peer){
              //切换插播视频和麦克风的声音
              return _.promise(function(ok, fail){
                if(this.isShare() && vShareStream.getAudioTracks().length>0){
                  peer = peer || sharePeer;
                  var track1 = vStream.getAudioTracks()[0];
                  var track2 = vCacheStream.getAudioTracks()[0];
                  if(peer){
                    peer.getSenders().forEach(function (sender) {
                      if(sender.track.kind=='audio'){
                        var track = null;
                        if(sender.track.id==track1.id){
                          track = track2;
                        }else{
                          track = track1;
                        }
                        sender.replaceTrack(track).then(function () {
                          ok();
                        }).catch(function (er) {
                          console.error('插播音频切换失败：'+er.message);
                          fail(er);
                        });
                      }
                    });
                  }
                  vStream.getAudioTracks().forEach(function(track){
                    vStream.removeTrack(track);
                    vStream.addTrack(track2);
                  });
                  vCacheStream.getAudioTracks().forEach(function(track){
                    vCacheStream.removeTrack(track);
                    vCacheStream.addTrack(track1);
                  });
                }else fail('');
              }.bind(this))
            },
            shareScreenshot: function (peer, show) {
              //屏幕截屏分享
              return fCheck(function (ok, fail) {
                var that = this;
                _D.mediaDevices.getDisplayMedia({video: true, audio: false}).then(function(stream) {
                  that.takePhoto(stream).then(function (base64) {
                    fCloseStream(stream);
                    that.shareResource(peer, base64, 1, show, true).then(function(e){
                      fEmitEvent('share', e);
                      ok(e)
                    }).catch(fail);
                  }).catch(function(e){
                    fCloseStream(stream);
                    fail(e);
                  });
                }).catch(fail);
              }, true)
            },
            shareResource: function (peer, resource, type, show, keep, loop) {
              //插播视频、图片资源
              return fCheck(function (ok, fail) {
                var that = this;
                var loaded = function (stream) {
                  if(stream.getAudioTracks().length>0){
                    var track = null;
                    if(keep){
                      track = fMixAudioTrack([stream.getAudioTracks()[0], vStream.getAudioTracks()[0]]);
                    }
                    stream.getAudioTracks().forEach(function(track){
                      stream.removeTrack(track);
                    });
                    if(track) stream.addTrack(track);
                  }
                  setTimeout(function(){
                    fStartShare(peer, stream, show);
                    ok(stream);
                  },100);
                };
                var error = function () {
                  fail(new Error('加载资源失败'));
                };
                var loadImage = function (res) {
                  var node = new Image();
                  node.src = res;
                  node.crossOrigin = "Anonymous"; //跨域处理
                  node.onload = function(){
                    fMakeStream(this, loaded, fail);
                  };
                  node.onerror = error;
                  return node;
                };
                var loadVideo = function (res) {
                  var node = document.createElement('video');
                  node.autoplay = true;
                  if(res instanceof MediaStream) node.srcObject = res;
                  else node.src = res;
                  node.loop = !!loop;
                  node.muted = keep==false;
                  node.setAttribute('playsinline', 'playsinline');
                  node.setAttribute('webkit-playsinline', 'webkit-playsinline');
                  node.oncanplay = function(){
                    node.play();
                  };
                  node.onloadedmetadata = function(){
                    loaded(this.captureStream(32));
                  };
                  node.ontimeupdate = function(){
                    if(!vShareStream) node.pause();
                  };
                  node.onended = function(){
                    logger.log('视频资源结束');
                    if(loop!=true) that.cancelShare();
                  };
                  node.onerror = error;
                  return node;
                };
                var initResource = function(resource){
                  if(resource instanceof MediaStream) loadVideo(resource);
                  else{
                    _.loadResource(resource).then(function(e){
                      if (/^image/.test(e.type)||type==1) {
                        loadImage(e.base64);
                      }else if (/^video/.test(e.type)||type==2) {
                        loadVideo(e.base64);
                      }else fail(new Error('资源不符合'));
                    }).catch(error);
                  }
                };
                if(keep!=false) keep = true;
                if(!resource){
                  var file = document.createElement('input');
                  file.type = 'file';
                  file.accept = type==1?'image/*':type==2?'video/*':'image/*; video/*';
                  file.click();
                  file.onchange = function(){
                    if(this.files.length>0) {
                      initResource(this.files[0]);
                      file.value='';
                    }
                  };
                }else initResource(resource);
              }, true)
            },
            cancelShare(){
              //取消桌面分享、桌面截图分享、插播分享
              fResetLocalStream();
              fCloseStream(vShareStream);
              vShareStream = null;
              sharePeer = null;
              fEmitEvent('cancelShare');
            },
            checkDevice: function () {
              //检测媒体设备是否可用
              fDeviceList(function () {
                var check = {
                  camera: false,
                  microphone: false,
                  speaker: vSpeakerList.length > 0
                };
                var checkCamera = function (index) {
                  if (index < cameraList.length) {
                    var device = cameraList[index];
                    fGetUserMedia({
                      video: {
                        deviceId: device.deviceId
                      },
                      audio: false
                    }).then(function (stream) {
                      check.camera = true;
                      fCloseStream(stream);
                      checkCamera(index + 1);
                    }).catch(function (e) {
                      checkCamera(index + 1);
                    })
                  } else {
                    checkMicro(0);
                  }
                };
                var checkMicro = function (index) {
                  if (index < microList.length) {
                    var device = microList[index];
                    fGetUserMedia({
                      audio: {
                        deviceId: device.deviceId
                      },
                      video: false
                    }).then(function (stream) {
                      check.microphone = true;
                      fCloseStream(stream);
                      checkMicro(index + 1);
                    }).catch(function (e) {
                      checkMicro(index + 1);
                    })
                  } else {
  
                  }
                };
                checkCamera(0);
              })
            },
            startCheck: function (fn, er) {
              var ctx = new AudioContext();
              var ana = ctx.createAnalyser();
              var chrunks = [];
              var tStream = null;
              var endAnimate = null;
              var ad = new Audio();
              var animate = function () {
                ana.getByteFrequencyData(chrunks);
                if (!ad.isPause) ad.volume = Math.random();
                fn((chrunks[0] / 256).toFixed(2), (chrunks[0] / 256).toFixed(2) * (ad.volume || 0));
                endAnimate = window.requestAnimationFrame(animate);
              };
              ana.minDecibels = -90,
                ana.maxDecibels = -40,
                ana.fftSize = 256;
              chrunks = new Uint8Array(ana.frequencyBinCount);
              this.open().then(function (stream) {
                var track = stream.getAudioTracks()[0];
                var ms = new MediaStream();
                var ss = null;
                tStream = stream;
                ms.addTrack(track);
                ss = ctx.createMediaStreamSource(ms);
                ss.connect(ana);
                animate();
                stream.addEventListener('inactive', function () {
                  ss.disconnect();
                  ana.disconnect();
                  ctx.close();
                  ad.pause();
                  window.cancelAnimationFrame(endAnimate);
                  fn(0, 0);
                });
                ad.srcObject = ms;
                ad.volume = 1;
                ad.autoplay = true;
              }).catch(er);
            },
            stopCheck: function () {
              yqWebrtcApp.Device.close();
            },
            isOpen: function () {
              //媒体是否处于打开状态
              return vStream != null && vStream.active;
            },
            bind: function () {
              //绑定事件
              var arg = {};
              if (arguments.length > 1) {
                var key = arguments[0];
                var callback = arguments[1];
                var once = false;
                if (arguments.length == 3) once = arguments[2];
                arg[key] = {
                  callback: callback,
                  once: once
                }
              } else if (arguments.length == 1) {
                arg = arguments[0];
                for (var key in arg) {
                  if (typeof arg[key] == 'function') {
                    arg[key] = {
                      callback: arg[key],
                      once: false
                    }
                  }
                }
              }
              fBindEvent(arg);
              return this;
            },
            unbind: function () {
              //解决事件
              fUnbindEvent.apply(this, arguments);
              return this;
            }
          };
          DeviceManager.setDefaultNoVideoImg(_C.DEFAULTNOVIDEOIMG, vImgMode);
          return DeviceManager;
        })((function () {
          var group = ['webkitGetUserMedia','mozGetUserMedia','msGetUserMedia','getUserMedia'];
          var flag = group.findIndex(function(item){ return item in navigator})!=-1;
          var getUserMedia = flag?navigator[group.filter(function(item){item in navigator})[0]]:null;
          if (!navigator.mediaDevices) {
            if (flag) {
              navigator.mediaDevices = {
                getUserMedia: function (constraints) {
                  return new Promise(function (resolve, reject) {
                    getUserMedia.call(navigator, constraints, resolve, reject);
                  });
                }
              };
            }
          } else {
            if (!navigator.mediaDevices.getUserMedia) flag = false;
            else flag = true;
          }
          return {
            mediaDevices: navigator.mediaDevices,
            isSupport: flag
          };
        })());
      return (function (Server) {
        var Cookie = (function (_T) {
          return {
            set: function (a, b, t, p) {
              var e = '',
                k = '';
              if (_.isNumber(t)) {
                var d = new Date();
                d.setTime(d.getTime() + t);
                e = _.isNumber(t) ? '; expires=' + d.toGMTString() : '';
              } else if (t) {
                p = t;
              }
              var k = '; path=' + (p ? p : '/');
              document.cookie = a + '=' + _T.decode(_.isObject(b) ? _.toJson(b) : b + '') + e + k;
            },
            get: function (a) {
              return this.map()[a];
            },
            map: function () {
              var r = document.cookie.split(';');
              var o = {};
              for (var i = 0; i < r.length; i++) {
                var t = r[i].replace(/^\s+|\s+$/g, '').split('=');
                if (t.length > 0) {
                  o[t[0]] = t.length > 1 ? _T.encode(t[1]) : '';
                }
              }
              return o;
            },
            has: function (a) {
              return this.get(a) != undefined;
            },
            clear: function (a) {
              if (a) this.set(a, '', -1);
              else {
                var o = this.map();
                for (var k in o) this.set(k, '', -1);
              }
            }
          }
        })({
          key: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ',
          decode: function (str) {
            var key = this.key;
            var l = key.length;
            var a = key.split("");
            var s = "",
              b, b1, b2, b3;
            for (var i = 0; i < str.length; i++) {
              b = str.charCodeAt(i);
              b1 = b % l;
              b = (b - b1) / l;
              b2 = b % l;
              b = (b - b2) / l;
              b3 = b % l;
              s += a[b3] + a[b2] + a[b1];
            }
            return s;
          },
          encode: function (str) {
            var key = this.key;
            var l = key.length;
            var b, b1, b2, b3, d = 0,
              s;
            s = new Array(Math.floor(str.length / 3));
            b = s.length;
            for (var i = 0; i < b; i++) {
              b1 = key.indexOf(str.charAt(d));
              d++;
              b2 = key.indexOf(str.charAt(d));
              d++;
              b3 = key.indexOf(str.charAt(d));
              d++;
              s[i] = b1 * l * l + b2 * l + b3
            }
            b = eval("String.fromCharCode(" + s.join(',') + ")");
            return b;
          }
        });
        var yqWebrtcApp = _.extend({
          version: '3.0.25.1',
          updateTime: '2023/03/29',
          config: function (config) {
            if (!serverContext) serverContext = new Server(config);
            return serverContext;
          },
          log: function (flag) {
            Logger.enable = !!flag;
          }
        }, {
          Server: Server,
          Device: Device,
          Timer: Timer,
          PeerConnection: PeerConnection,
          Define: Define,
          Utils: _,
          Cookie: Cookie,
          Player: Player
        });
        if (typeof window.noGlobal === "undefined") {
          window.yqWebrtcApp = yqWebrtcApp;
        }
        return yqWebrtcApp;
      })(
        Define({
          /**
           * 通信服务
           */
          static: {
            //静态场景类型定义
            //视频通话
            VIDEOCHAT: 'videochat',
            //语音通话
            AUDIOCALL: 'audiocall',
            //网络视频通话
            NETVIDEOCHAT: 'netVideoChat',
            //视频会议
            VIDEOMEETING: 'videomeeting',
            //语音会议
            AUDIOMEETING: 'audiomeeting',
            //屏幕共享
            SHARESCREEN: 'sharesreen',
            //白板共享
            SHAREWHITEBOART: 'sharewhiteboart'
          },
          inited: function (ctx) {
            //初始化
            return function (config) {
              _.extend(ctx.config, config);
              _.observer({
                context: this,
                scope: ctx
              }, ctx.commandEvents);
              if (config.turnServer) _.extend(ctx.turnServer, config.turnServer);
              ctx.checkConfig(ctx.config, function () {
                ctx.heart = new Timer({
                  log: ctx.config.log,
                  duration: ctx.config.heartDuration,
                  postData: {
                    tip: '服务定时器'
                  }
                });
                ctx.heart
                  .onStart(function (e) {
                    ctx.logger.info(e);
                  }).onStop(function (e) {
                    ctx.logger.info(e);
                  }).onPause(function (e) {
                    ctx.logger.info(e);
                  }).onResume(function (e) {
                    ctx.logger.info(e);
                  }).onRun(function (e) {
                    ctx.logger.info(e);
                    ctx.send('heart');
                  });
              });
              ctx.logger = new Logger({
                log: ctx.config.log
              });
              ctx.logger.log('服务配置项'+_.toJson(ctx.config));
            }
          },
          events: _C.SERVER_EVENT,
          private: {
            socket: null, //websocket上下文对象
            sessionId: '', //服务会话唯一ID
            config: { //服务配置项
              heartDuration: 30000, // 服务心跳频率，默认30秒
              aliveDuration: 30000, //会话保活时长，默认30秒
              terminalType: 'PC', // 终端类型，分别为PC,Android,iOs,miniProgram,wChat，默认PC
              reconnectDuration: 5000, // 服务重连频率，默认5秒
              reconnectCount: 10, // 服务重连次数，默认10次
              reconnect: true, // 服务是否启动重连，默认true
              oneWaitTime: 2 * 60 * 1000, // 相对会议场景时，会议只有一人时自动结束时长，默认为2分
              postData: {}, // 传递数据
              optional: {}, //可通过服务配置项设置通道设置项
              turnServer: null, // 指定turnServer
              log: false, // 是否打印日记
              videoEnabled: true, //是否默认打开摄像头
              audioEnabled: true, //是否默认打开麦克风
              cameraEnabled: true, //是否禁用摄像头
              mikeEnabled: true, //是否禁用麦克风
              keepUserConnect: '', //保持上一次注册用户会话连接，该配置针对外层业务重连使用，传userId值
              autoConnect: true //当网络正常后是否自动连接服务
            },
            registerSenceMap: {}, //缓存会话场景集
            logger: null, //日记输出对象
            isReconnect: false, //是否启动了重连机制，分两种情况，一种是启动服务后的重连，一种是未启动服务的重连
            isClose: false, //是否主动关闭，如果是不会触发重连机制
            isForce: false, //是否被强制下线
            isNetworkDown: false, //网络是否断开
            userInfo: null, //websocket鉴权成员信息
            tokenInfo: null, //注册信息
            trtcInfo: null, //TRTC信息
            connectCount: 0, //重连计数
            heart: null, //服务心跳对象
            senceTypes: ['videochat', 'audiocall', 'netVideoChat', 'videomeeting', 'audiomeeting', 'sharescreen', 'sharewhiteboart'], //会话场景类型，videochat视频通话 audiocall语音通话 videomeeting视频会议 audiomeeting语音会议 sharescreen分享屏幕 sharewhiteboart分享白板
            senceMap: [], //当前正在执行的会话场景集合
            network: { //网络信息
              state: 0, //网络状态
              speed: 0 //网络速度
            },
            turnServer: { //turnServer信息
              iceServers: []
            },
            networkState: {
              online: false,
              offline: false
            },
            thread: null, //重连线程对象
            userList: [], //在线用户列表
            serverEvents: {}, //服务事件集
            commandEvents: { //指令事件集
              'evt_authen': function (e) {
                //鉴权成功
                this.scope.sessionId = e.sessionId;
                this.scope.userInfo = {
                  pbxUserId: e.pbxUserId,
                  rtcAccountID: e.rtcAccountId,
                  sipPhone: e.sipPhone,
                  userId: e.userId
                };
                if (e.sdkAppId) {
                  this.scope.trtcInfo = {
                    sdkAppId: e.sdkAppId,
                    userSig: e.userSig
                  };
                }
                if (this.scope.config.turnServer == null) {
                  this.scope.turnServer.iceServers = [];
                  if (e.stunServerUrl) this.scope.turnServer.iceServers.push({
                    urls: [e.stunServerUrl],
                    url: e.stunServerUrl
                  });
                  if (e.turnServerUrl) {
                    this.scope.turnServer.iceServers.push({
                      urls: [e.turnServerUrl],
                      url: e.turnServerUrl,
                      username: e.turnServerAcct,
                      credential: e.turnServerPwd
                    });
                  }
                  if (e.turnServerArray) {
                    for (var i = 0; i < e.turnServerArray.length; i++) {
                      var td = e.turnServerArray[i];
                      this.scope.turnServer.iceServers.push({
                        urls: [td],
                        url: td,
                        username: e.turnServerAcct,
                        credential: e.turnServerPwd
                      });
                    }
                  }
                }
                //获取在线用户
                this.scope.logger.log('获取在线用户列表');
                this.scope.send('queryRTCUserList');
                if (this.scope.isReconnect) {
                  //如果重连时，唤醒服务心跳
                  this.scope.logger.log('唤醒服务心跳');
                  if(this.scope.heart.isLive()) this.scope.heart.resume();
                  else this.scope.heart.start();
                  this.scope.isReconnect = false;
                  // this.scope.execCommand({
                  //   command: 'evt_reconnect'
                  // });
                } else {
                  this.scope.logger.log('启动服务心跳');
                  this.scope.heart.start();
                }
                this.scope._callback('state', _U.serverManager(10009, {
                  state: true
                }));
                this.scope._callback('ready', _U.serverManager(10007, {
                  sessionId: e.sessionId,
                  reconnect: this.scope.isReconnect,
                  user: this.scope.userInfo
                }));
              },
              'evt_private': function (e, isOk) {
                //动态数据透传
                if (isOk) {
                  this.scope._callback('privateData', _U.serverManager(10018, {
                    user: e.member,
                    privateData: e.privateData
                  }));
                }
              },
              'resp_heart': function (e, isOk) {
                if (!isOk) {
                  //心跳响应失败
                  //主动关闭服务
                  this.scope.isClose = true;
                  this.scope.socket.close();
                }
              },
              'evt_online': function (e, isOk) {
                //上线通知
                if (isOk) {
                  var tUser = this.scope.userList.find(function (user) {
                    return user.userId == e.member.userId;
                  });
                  if (tUser) {
                    tUser.isFree = e.member.status == _C.STATE.free;
                  } else {
                    tUser = this.scope.formatUserInfo(e.member);
                    this.scope.userList.push(tUser);
                  }
                  this.scope._callback('online', _U.serverManager(10011, {
                    user: tUser
                  }));
                  this.scope._callback('list', _U.serverManager(10010, {
                    list: this.scope.userList
                  }));
                }
              },
              'evt_offline': function (e, isOk) {
                //下线通知
                if (isOk) {
                  this.scope.userList = this.scope.userList.filter(function (user) {
                    return user.userId != e.member.userId;
                  });
                  this.scope._callback('list', _U.serverManager(10010, {
                    list: this.scope.userList
                  }));

                  if (e.member.userId == this.scope.userInfo.userId) {
                    //被强制下线，不重连
                    this.scope.reset();
                    this.scope.isForce = true;
                    this.scope._callback('offline', _U.serverManager(10012, {
                      user: this.scope.formatUserInfo(e.member),
                      tip: e.tip
                    }));
                    this.scope._callback('stop', _U.serverManager(10006));
                    this.scope._callback('error', _U.serverManager(10016));
                  }
                }
              },
              'evt_user_status_change': function (e, isOk) {
                //用户状态
                if (isOk) {
                  this.scope._callback('userStatus', _U.serverManager(10013, {
                    user: e.member,
                    state: e.code,
                    isFree: e.code == _C.STATE.free
                  }));
                }
              },
              'resp_queryRTCUserList': function (e, isOk) {
                //在线用户列表
                if (isOk) {
                  var vThis = this;
                  this.scope.userList = e.userList.map(function (user) {
                    return vThis.scope.formatUserInfo(user);
                  });
                  this.scope._callback('list', _U.serverManager(10010, {
                    list: this.scope.userList
                  }));
                }
              },
              'evt_sip_register': function (e, isOk) {
                //sip话机状态
              },
              'resp_propertie': function (e, isOk) {
                if (!isOk) {
                  this.scope._callback('fail', _U.serverManager(10021))
                }
              },
              'evt_call_status_change': function(e, isOk){
                //通话状态
                if(isOk){
                  this.scope._callback('callStatus', _U.serverManager(10023, {
                    status: e.code
                  }));
                }
              }
            },
            execCommand: function (ctx) {
              //执行指令事件
              return function (data) {
                var tThis = this;
                var tCommand = data.command;
                var tResult = data.result;
                var tCallback = null;
                tThis.logger.info({
                  tip: '收到websocket消息',
                  data: data
                });
                if (tCommand in tThis.commandEvents) {
                  tCallback = tThis.commandEvents[tCommand];
                  if (_.isObject(tCallback)) {
                    tThis.senceMap.forEach(function (sence) {
                      if (sence in tCallback) {
                        tCallback = tCallback[sence];
                      }
                    });
                  }
                  if (_.isFunction(tCallback)) {
                    tResult != undefined ? tCallback(data, tResult == _C.STATE.ok) : tCallback(data);
                  }
                } else {
                  for (var sence in tThis.registerSenceMap) {
                    tThis.registerSenceMap[sence].forEach(function (context) {
                      var tCallId = data.callId || data.confId;
                      var tSerialId = data.serialId;
                      var command = data.command;
                      var tEvents = tThis.commandEvents[context.getId()];
                      if (tEvents) {
                        if (command in tEvents) {
                          tCallback = tEvents[command];
                          if (_.inArray(function (item) {
                              return item == command;
                            }, ['offer_petra_audio', 'offer_one2one', 'offer_petra_video', 'evt_invite']) ||
                            _.inArray(function (item) {
                              return item == command;
                            }, ['answer_meeting']) && _.inArray(function (item) {
                              return item.userId == data.from.userId;
                            }, context.getMemberList()) ||
                            _.inArray(function (item) {
                              return item == command;
                            }, ['resp_offer_petra_audio', 'resp_offer_one2one', 'resp_offer_petra_video', 'resp_bind']) && context.getSerialId() == tSerialId || tCallId && context.getCallId() == tCallId && context.getSence() == sence) {
                            tThis.logger.log('场景类型：<' + sence + '> 场景ID：<' + context.getId() + '> 指令：<' + command + '> 开始执行场景会话事件回调');
                            if (_.isFunction(tCallback)) {
                              tResult != undefined ? tCallback(data, tResult == _C.STATE.ok) : tCallback(data, true);
                            }
                          } else {
                            if (_.isFunction(tCallback)) {
                              tResult != undefined ? tCallback(data, tResult == _C.STATE.ok) : tCallback(data, true);
                            }
                          }
                        }
                      }
                    })
                  }
                }
              }
            },
            send: function (ctx) {
              //发送信息
              return function (command, data) {
                if (ctx.isLive()) {
                  //返回流水ID，主要用于区别会话
                  var tData = {
                    command: command,
                    timestamp: _.timestamp(),
                    serialId: _.serialId(),
                    sender: this.config.sender
                  };
                  _.extend(tData, data || {});
                  var tMsg = _.toJson(tData);
                  this.logger.info('向websocket发送消息：' + tMsg);
                  this.socket.send(tMsg);
                  return tData.serialId;
                }
                return null;
              }
            },
            checkSession: function (ctx) {
              //检查会话
              return function (fn, flag) {
                if (ctx.isLive()) {
                  if (fn) return fn.call(ctx);
                } else {
                  if (flag && fn) return fn.call(ctx);
                  else if(!this.isClose){
                    this.logger.warn('未连接服务');
                  }
                }
              }
            },
            checkConfig: function (ctx) {
              //检查配置项
              return function (option, fn) {
                var tKeys = [];
                _C.SERVER_CONFIG.split(_C.REGEXP.split).forEach(function (item) {
                  if (!(item in option)) {
                    tKeys.push(item);
                  }
                });
                if (tKeys.length == 0) fn();
                else this._callback('error', this.formatReason(10001, tKeys));
              }
            },
            formatReason: function (ctx) {
              //格式化事件数据
              return function (code, reason) {
                return _U.callbackMsg(code, 'ServerManager', 'server');
              }
            },
            formatUserInfo: function (ctx) {
              //格式化用户状态
              return function (user) {
                return _.extend({
                  isFree: user.status == _C.STATE.free
                }, user);
              }
            },
            bindSocketEvent: function (ctx) {
              //websocket事件绑定
              return function (target) {
                var vThis = this;
                _.each({
                  open: function (e) {
                    vThis.logger.log('连接websocket成功');
                    if (vThis.thread) {
                      vThis.thread.stop();
                    }
                  },
                  close: function (e) {
                    vThis.logger.warn('websocket已关闭:'+e.code);
                    if (vThis.isClose||vThis.isReconnect||vThis.isForce) {
                      return;
                    }
                    //非主动关闭时
                    if (vThis.sessionId) {
                      //曾连接成功过
                      vThis.logger.log('未正常断开服务，开始重连服务...');
                    } else {
                      vThis.logger.log('未连接服务成功，再次尝试连接服务...');
                    }
                    vThis._callback('state', _U.serverManager(10009, {
                      state: false
                    }));
                    vThis._callback('error', _U.serverManager(10017));
                    //重连服务
                    vThis.connectCount = 0;
                    vThis.heart.pause();
                    vThis.restartServer();
                  },
                  error: function (e) {
                    vThis.logger.log('连接websocket异常');
                    vThis.reset();
                  },
                  message: function (e) {
                    vThis.logger.log('收到websocket消息');
                    if (_.isString(e.data)) {
                      var tData = _.toObject(e.data);
                      vThis.execCommand(tData);
                      if (tData.result == _C.STATE.fail) {
                        vThis._callback('fail', _U.serverManager(10008, tData));
                      }
                    } else {
                      //二进制流
                    }
                  }
                }, function (type, callback) {
                  _.bindEvent(target, type, callback);
                });
              }
            },
            startServer: function (ctx) {
              //启动服务
              return function () {
                var vThis = this;
                vThis.getToken(function (e) {
                  var tData = {
                    accessSender: vThis.config.sender,
                    accessToken: e.accessToken,
                    accessTimestamp: e.timestamp
                  };
                  var tUrl = '';
                  vThis.tokenInfo = e;
                  if (vThis.isReconnect||vThis.config.keepUserConnect) {
                    //重连
                    if(vThis.config.keepUserConnect) vThis.userInfo = { userId: vThis.config.keepUserConnect};
                    if (vThis.userInfo) {
                      vThis.logger.log('会话重连...');
                      tData.reconnect = true;
                      tData.userId = vThis.userInfo.userId;
                    } else {
                      vThis.logger.log('服务重连...');
                    }
                  } else {
                    vThis.logger.log('开始连接websocket...');
                  }
                  tUrl = e.webrtcServerUrl + _.toParams(tData, true);
                  vThis.socket = new(WebSocket || MozWebSocket)(tUrl);
                  vThis.bindSocketEvent(vThis.socket);
                });
              }
            },
            restartServer: function (ctx) {
              //重连服务
              return function () {
                var vThis = this;
                if(vThis.isClose){
                  vThis.logger.warn('已主动关闭服务，不再重连');
                  vThis.reset();
                  return;
                }
                if (vThis.config.reconnectCount!=-1 && vThis.connectCount >= vThis.config.reconnectCount) {
                  //超过重连次数，重连失败
                  vThis.reset();
                  vThis.isClose = true;
                  vThis.logger.error('重连服务失败');
                  vThis._callback('error', _U.serverManager(10007));
                  vThis.execCommand({
                    command: 'evt_connect_error',
                    tip: '重连失败'
                  });
                } else {
                  vThis.isReconnect = true;
                  vThis.logger.log('等待' + vThis.config.reconnectDuration + '毫秒后重连');
                  Timer.sleep(vThis.config.reconnectDuration).then(function () {
                    vThis.connectCount += 1;
                    vThis.logger.warn({
                      count: vThis.connectCount,
                      tip: '重连服务第' + vThis.connectCount + '次'
                    });
                    vThis.startServer();
                  });
                  //缓存当前重连线程对象
                  vThis.thread = Timer.currentThread;
                }
              }
            },
            getToken: function (ctx) {
              //请求认证
              return function (fn) {
                var vThis = this;
                vThis.http('getToken', {
                  loginAcct: vThis.config.account,
                  loginPwd: vThis.config.password ? _.cryptoDes(vThis.config.password, vThis.config.deskey) : '',
                  terminalType: vThis.config.terminalType,
                  userData: vThis.config.postData
                }, function (e) {
                  vThis.connectCount = 0;
                  vThis.logger.warn('请求getToken成功');
                  fn(e);
                }, function (e) {
                  vThis.logger.error('请求getToken失败');
                  if (vThis.isReconnect) vThis.restartServer();
                  vThis._callback('error', _U.serverManager(vThis.isReconnect ? 10017 : 10004));
                });
              }
            },
            http: function (ctx) {
              //http请求
              return function (command, data, ok, fail) {
                var tThis = this;
                var tData = {
                  command: command,
                  sender: tThis.config.sender,
                  timestamp: _.timestamp(),
                  serialId: _.serialId()
                };
                tData.verifyData = _.cryptoDes([tThis.config.sender, tThis.config.desPassword, command, tData.timestamp, tData.serialId].join(''), tThis.config.deskey);
                if (_.isObject(data)) {
                  Object.assign(tData, data);
                }
                _.request({
                  url: /http|https/.test(command) ? command : tThis.config.url.replace(/^\s+|\s+$/g, '') + (/\/openApi$/.test(tThis.config.url) ? '' : '/openApi'),
                  type: 'POST',
                  dataType: 'json',
                  data: _.toJson(tData),
                  header: {
                    'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
                  },
                  success: function (e) {
                    if (e.respCode === _C.STATE.ok) {
                      e.timestamp = tData.timestamp;
                      ok(e);
                    } else {
                      tThis._callback('error', _U.serverManager(10004));
                    }
                  },
                  error: function (e) {
                    fail(e);
                  },
                  timeout: function (e) {
                    fail(e);
                  }
                });
              }
            },
            reset: function (ctx) {
              //重置
              return function () {
                if(this.socket) this.socket.close();
                if(this.heart) this.heart.stop();
                if (this.thread) this.thread.stop();
                this.connectCount = 0;
                this.socket = null;
                this.isForce = false;
                this.isReconnect = false;
              }
            },
            destorySence: function (ctx) {
              //销毁场景会话
              return function () {
                _.each(this.registerSenceMap, function (name, item) {
                  item.forEach(function (context) {
                    context.destory();
                  });
                });
              }
            },
            printVersion: function(ctx){
              return function(){
                console.info('SDK version:', yqWebrtcApp.version);
                console.info('SDK update time:', yqWebrtcApp.updateTime);
                console.info('Broswer userAgent:', navigator.userAgent);
                console.info('Viewport:', window.innerWidth+'*'+window.innerHeight);
                console.info('Webrtc support:', Device.isSupport());
                console.info('Constraint support:', navigator.mediaDevices.getSupportedConstraints());
              }
            },
            bindNetworkEvent: function(ctx){
              return function(){
                var that = this;
                var events = null;
                var tm = null;
                var online = function(){
                  if(that.networkState.online||!navigator.onLine) return;
                  that.networkState.offline = false;
                  that.networkState.online = true;
                  that.isNetworkDown = false;
                  that.logger.warn('网络正常');
                  that._callback('network', _U.serverManager(10021, {
                    state: true
                  }));
                  if(!ctx.isLive()&&!that.isReconnect&&!that.isClose&&that.autoConnect) {
                    //服务不存活、不重连状态、不是主动关闭行为、允许自动网络恢复重连
                    that.logger.warn('网络恢复开始重连服务');
                    that.reset();
                    that.startServer();
                  }
                };
                var offline = function(){
                  if(that.networkState.offline||navigator.onLine) return;
                  that.networkState.online = false;
                  that.networkState.offline = true;
                  that.isNetworkDown = true;
                  that.logger.warn('网络断开');
                  that._callback('network', _U.serverManager(10020, {
                    state: false
                  }));
                  if(ctx.isLive()){
                    that.reset();
                  }
                };
                if(navigator.connection){
                    events = {
                        change: function(){
                          if(tm) clearTimeout(tm);
                          tm = setTimeout(function(){
                            if(navigator.onLine) online();
                            else offline();
                          });
                          that._callback('networkType', _U.serverManager(10025, {type: this.type||this.effectiveType}));
                          that._callback('networkData', _U.serverManager(10024, {
                              downlink: this.downlink,
                              rtt: this.rtt,
                              saveData: this.saveData
                          }));
                        },
                        typechange: function(){
                          that._callback('networkType', _U.serverManager(10025, {type: this.type||this.effectiveType}));
                        }
                    };
                    for(var key in events){
                      navigator.connection.addEventListener(key, events[key]);
                    }
                }else{
                    events = {
                        online: online,
                        offline: offline
                    };
                    for(var key in events){
                      window.addEventListener(key, events[key]);
                    }
                }
                that.isNetworkDown = !navigator.onLine;
                that.networkState.online = false;
                that.networkState.offline = false;
              }
            }
          },
          public: {
            start: function (ctx) {
              //启动服务
              return function () {
                ctx.printVersion();
                ctx.checkConfig(ctx.config, function () {
                  ctx.reset();
                  ctx.logger.log('开始启动服务...');
                  ctx.checkSession(function () {
                    ctx.isClose = false;
                    ctx.bindNetworkEvent();
                    Device.setVideoEnabled(ctx.config.videoEnabled);
                    Device.setAudioEnabled(ctx.config.audioEnabled);
                    Device.setCameraEnabled(ctx.config.cameraEnabled);
                    Device.setMikeEnabled(ctx.config.mikeEnabled);
                    ctx.startServer();
                  }, true);
                });
              }
            },
            stop: function (ctx) {
              //停止服务
              return function () {
                ctx.logger.log('主动关闭服务');
                ctx.reset();
                ctx.isClose = true;
                ctx.sessionId = '';
                ctx.checkSession(function () {
                  if(Device.isOpen()) Device.close();
                  ctx.userInfo = null;
                  ctx._callback('stop', _U.serverManager(10006));
                }, true);
              }
            },
            isLive: function (ctx) {
              //服务是否存活
              return function () {
                return !!(ctx.socket && ctx.socket.readyState == ctx.socket.OPEN && ctx.sessionId);
              }
            },
            hasNetwork: function (ctx) {
              //是否有网络
              return function () {
                return navigator.onLine;
              }
            },
            sendCommand: function (ctx) {
              //发送指令数据
              return function (command, data) {
                return ctx.checkSession(function () {
                  return ctx.send(command, data);
                }) || null
              }
            },
            send: function (ctx) {
              //发送字符串数据或二进制数据
              return function (data) {
                return ctx.checkSession(function () {
                  ctx.socket.send(data);
                }) || null
              }
            },
            postData: function (ctx) {
              return function (data) {
                //透传数据并通知给其他用户
                ctx.send('set_private', {
                  privateData: _.isObject(data) ? _.toJson(data) : data + ''
                })
              }
            },
            requestCommand: function (ctx) {
              //发送http指令请求
              return function (command, data) {
                return new Promise(function (resolve, reject) {
                  ctx.http(command, data, resolve, reject);
                })
              }
            },
            getSocket: function (ctx) {
              //获取websocket上下文对象
              return function () {
                return ctx.socket;
              }
            },
            getConfig: function (ctx) {
              //获取配置项
              return function () {
                return ctx.config;
              }
            },
            getUserInfo: function (ctx) {
              //获取鉴权用户信息
              return function () {
                return ctx.userInfo;
              }
            },
            getTokenInfo: function (ctx) {
              //获取认证信息
              return function () {
                return ctx.tokenInfo;
              }
            },
            getSessionId: function (ctx) {
              //获取服务会话ID
              return function () {
                return ctx.sessionId;
              }
            },
            getLogger: function (ctx) {
              //获取日记对象
              return function () {
                return ctx.logger;
              }
            },
            getTurnServer: function (ctx) {
              //获取turnServer信息
              return function () {
                return ctx.turnServer;
              }
            },
            getUserList: function (ctx) {
              //获取在线用户列表
              return function () {
                return ctx.userList;
              }
            },
            updateUserList: function (ctx) {
              //更新在线用户列表
              return function () {
                ctx.send('queryRTCUserList');
              }
            },
            registerSence: function (ctx) {
              //注册会话场景
              return function (sence, context) {
                if (!ctx.registerSenceMap[sence]) ctx.registerSenceMap[sence] = [];
                ctx.registerSenceMap[sence].push(context);
              }
            },
            getRegisterSence: function (ctx) {
              //获取注册会话场景
              return function () {
                return ctx.registerSenceMap;
              }
            },
            getCommandEvents: function (ctx) {
              //获取指令事件集合，正式版会去掉
              return function () {
                return ctx.commandEvents;
              }
            },
            bindCommand: function (ctx) {
              //绑定业务场景指令
              return function (context, command, callback) {
                if (context && context.getSence && context.getSence()) {
                  if (_.isString(command) && _.isFunction(callback)) {
                    var tObj = {};
                    tObj[command] = callback;
                    command = tObj;
                  }
                  if (_.isObject(command)) {
                    _.each(command, function (name, data) {
                      var tSenceId = context.getId();
                      if (!ctx.commandEvents[tSenceId]) ctx.commandEvents[tSenceId] = {};
                      ctx.commandEvents[tSenceId][name] = command[name];
                    })
                  }
                }
              }
            },
            unbindCommand: function (ctx) {
              //解除绑定业务场景指令
              return function (context, command, callback) {

              }
            },
            checkUserStatus: function (ctx) {
              //检查用户状态
              return function (user) {
                return ctx.checkSession(function () {
                  var tUser = ctx.userList.find(function (item) {
                    return item.userId == user.userId;
                  });
                  return tUser && tUser.isFree;
                })
              }
            },
            createVideoChat: function (ctx, struc) {
              //创建视频通话
              return function (option) {
                var tHandler = new VideoChat(option, this);
                this.registerSence(struc.VIDEOCHAT, tHandler);
                return tHandler;
              }
            },
            createAudioCall: function (ctx, struc) {
              //创建语音通话
              return function (option) {
                var tHandler = new AudioCall(option, this);
                this.registerSence(struc.AUDIOCALL, tHandler);
                return tHandler;
              }
            },
            createNetVideoChat: function (ctx, struc) {
              //创建网络视频通话
              return function (option) {
                var tHandler = new NetVideoChat(option, this);
                this.registerSence(struc.NETVIDEOCHAT, tHandler);
                return tHandler;
              }
            },
            createVolteCall: function(ctx, struc){
              //创建Volte音视频通话
              return function(option){
                var tHandler = new VolteCall(option, this);
                this.registerSence(struc.VOLTECALL, tHandler);
                return tHandler;
              }
            },
            createWhiteboard: function (ctx, struc) {
              //创建电子签名，即白板
              return function (option) {
                var tHandler = new Whiteboard(option, this);
                return tHandler;
              }
            },
            createVideoMeeting: function (ctx, struc) {
              //创建视频会议
              return function (option) {
                var tHandler = new VideoMeeting(option, this);
                this.registerSence(struc.VIDEOMEETING, tHandler);
                return tHandler;
              }
            },
            createAudioMeeting: function (ctx, struc) {
              //创建音频会议
              return function (option) {
                var tHandler = new VideoMeeting(option, this);
                this.registerSence(struc.VIDEOMEETING, tHandler);
                return tHandler;
              }
            }
          }
        }))
    })(function (option) {
        return new(function (option) {
          function Builder() {
            var g = this;
            var vCallback = _.copyObject(_.isString(option.events) ? option.events.split(_C.REGEXP.split) : _.isArray(option.events) ? option.events : []);
            var vPrivate = _.copyObject(option.private, true);
            var vPublic = _.copyObject(option.public, true);
            var vArgs = [];
            var vId = _.uuid();
            var vData = {};
            var vEvents = {};
            vCallback.forEach(function (name) {
              var tName = 'on' + name.charAt(0).toUpperCase() + name.substr(1);
              try {
                g[tName] = eval('(function(callback){vCallback["' + name + '"]=callback; return this;})');
              } catch (e) {
                new Error('build object error');
              }
            });
            //增加不可覆盖私有方法
            _.extend(vPrivate, {
              _callback: function (ctx) {
                //执行回调方法
                return function (name, data) {
                  if (this._hasCallback(name)) {
                    this._trigger(name, data);
                    return vCallback[name].call(ctx, data, vData);
                  }
                }
              },
              _trigger: function (ctx) {
                //执行绑定事件
                return function (name, data) {
                  if (name in vEvents) {
                    vEvents[name].forEach(function (item) {
                      item.callback.call(ctx, data, vData);
                      if (item.once) {
                        ctx._unbind(name, item.callback);
                      }
                    })
                  }
                }
              },
              _hasCallback: function (ctx) {
                return function (name) {
                  return name in vCallback;
                }
              }
            });
            _.each(vPrivate, function (name, data) {
              if (_.isFunction(data)) {
                vPrivate[name] = data.call(g, g, Builder);
              }
            });
            //增加不可覆盖公共方法
            _.extend(vPublic, {
              _postData: function (ctx) {
                //设置或获取透传数据
                return function () {
                  if (arguments.length > 0) vData = arguments[0];
                  return vData;
                }
              },
              _getId: function (ctx) {
                //对象唯一ID
                return function () {
                  return vId;
                }
              },
              _getPrivateAttribute: function (ctx) {
                //获取私有属性副本，方便查找数据
                return function () {
                  return _.copyObject(vPrivate, true);
                }
              },
              _bind: function (ctx) {
                //动态绑定事件
                return function (name, callback, once) {
                  //once表示只执行一次就会被解绑
                  if (_.isString(name) && _.isFunction(callback)) {
                    if (!vEvents[name]) {
                      vEvents[name] = [];
                    }
                    vEvents[name].push({
                      callback: callback,
                      once: !!once
                    });
                  }
                }
              },
              _unbind: function (ctx) {
                //解除绑定事件
                return function (name, callback) {
                  if (name && vEvents[name]) {
                    if (callback) {
                      vEvents[name] = vEvents[name].filter(function (item) {
                        return item != callback;
                      });
                    } else {
                      try {
                        vEvents[name] = null;
                        delete vEvents[name];
                      } catch (e) {}
                    }
                  }
                }
              }
            });
            _.each(vPublic, function (name, data) {
              g[name] = _.isFunction(data) ? data.call(g, vPrivate, Builder) : data;
            });
            vArgs = arguments;
            option.inited.call(g, vPrivate, Builder).apply(g, _.argToArray(vArgs));
            if (_.isFunction(option.extends)) {
              option.extends.apply(g, vArgs);
            } else if (_.isObject(option.extends)) {
              if (_.isFunction(option.extends.name)) {
                vArgs = option.extends.arguments;
                if (_.isFunction(vArgs)) vArgs = vArgs.call(g, vPrivate, Builder);
                option.extends.name.apply(g, vArgs);
                if (option.extends.super) {
                  option.extends.super.call(g, vPrivate, Builder);
                }
              }
            }
          }
          _.each(option.static, function (name, data) {
            Builder[name] = _.isFunction(data) ? data.call(option.static, Builder) : data;
          });
          return Builder;
        })(_.extend({
          private: {},
          public: {},
          static: {},
          callback: [],
          inited: function () {}
        }, option || {}));
      },
      (function (msg) {
        return {
          callbackMsg: function (code, type, data) {
            return msg(code, type, data)
          },
          videoChat: function (code, data) {
            return msg(code, _C.KEYS.VIDEOCHAT, data);
          },
          audioCall: function (code, data) {
            return msg(code, _C.KEYS.AUDIOCALL, data);
          },
          netVideoChat: function (code, data) {
            return msg(code, _C.KEYS.NETVIDEOCHAT, data);
          },
          videoMeeting: function (code, data) {
            return msg(code, _C.KEYS.VIDEOMEETING, data);
          },
          audioMeeting: function (code, data) {
            return msg(code, _C.KEYS.AUDIOMEETING, data);
          },
          mediaDevice: function (code, data) {
            return msg(code, _C.KEYS.MEDIADEVICE, data);
          },
          serverManager: function (code, data) {
            return msg(code, _C.KEYS.SERVERMANAGER, data);
          },
          timer: function (code, data) {
            return msg(code, _C.KEYS.TIMER, data);
          },
          stats: function (code, data) {
            return msg(code, _C.KEYS.STATS, data);
          },
          peerConnection: function (code, data) {
            return msg(code, _C.KEYS.PEERCONNECT, data);
          }
        }
      })(function (code, type, data) {
        var tTip = '';
        if (_C.TIP[type] && _C.TIP[type][code]) {
          tTip = _C.TIP[type][code];
        }
        return {
          msgCode: code,
          msgType: type,
          msgTip: tTip,
          msgTimestamp: _.timestamp(),
          msgSerialId: _.serialId(),
          msgData: data
        }
      })
    )
  })({
    objectType: function (variable) {
      return Object.prototype.toString.call(variable).replace(/^\[object ([^\]]+)\]$/g, '$1').toLowerCase()
    },
    isString: function (variable) {
      return this.objectType(variable) === 'string'
    },
    isObject: function (variable) {
      return this.objectType(variable) === 'object'
    },
    isArray: function (variable) {
      return this.objectType(variable) === 'array'
    },
    isNumber: function (variable) {
      return this.objectType(variable) === 'number'
    },
    isBoolean: function (variable) {
      return this.objectType(variable) === 'boolean'
    },
    isFunction: function (variable) {
      return this.objectType(variable) === 'function'
    },
    isNull: function (variable) {
      return !!variable && !this.isNumber(variable)
    },
    userAgent: function () {
      return navigator.userAgent
    },
    isIos: function () {
      return /ipad|ios|iphone/i.test(this.userAgent())
    },
    isIpad: function () {
      return /ipad/i.test(this.userAgent())
    },
    isMac: function () {
      return /\smac\s/i.test(this.userAgent()) && !this.isMobile()
    },
    isAndroid: function () {
      return /android/i.test(this.userAgent())
    },
    isMobile: function (flag) {
      var mobile = /ipad|ios|iphone|mobile|android/i.test(this.userAgent());
      return flag==false?mobile:mobile&&(/linux/i.test(navigator.platform)||/iphone|mac/i.test(navigator.platform));
    },
    inArray: function (callback, array) {
      for (var i = 0; i < array.length; i++) {
        if (callback(array[i], i) == true) return true;
      }
      return false;
    },
    toJson: function (object) {
      try {
        return JSON.stringify(object);
      } catch (e) {
        return '';
      }
    },
    toObject: function (str) {
      try {
        return JSON.parse(str);
      } catch (e) {
        return {}
      }
    },
    toParams: function (object, flag) {
      var vStr = flag ? '?' : '';
      for (var key in object) {
        vStr += key + '=' + object[key] + '&';
      }
      return vStr.replace(/&$/, '');
    },
    toQuery: function(data) {
      var obj = {};
      var index = -1;
      data = data||location.href;
      index = data.indexOf('?');
      if (index !== -1) {
          data = data.substr(index + 1);
          var arr = data.split('&');
          this.each(arr, function (item) {
              var td = item.split('=');
              obj[td[0]] = td.length > 1 ? td[1] : '';
          });
      }
      return obj;
    },
    copyObject: function (object, flag) {
      var vThis = this;
      var vObj = new Object();
      if (this.isArray(object)) {
        vObj = new Array();
        object.forEach(function (item) {
          vObj.push(item);
        });
      } else if (this.isObject(object)) {
        //浅复制
        vObj = new Object();
        for (var name in object) {
          vObj[name] = object[name];
          if (flag) {
            if (this.isObject(vObj[name])) {
              vObj[name] = this.copyObject(vObj[name]);
            } else if (this.isArray(vObj[name])) {
              var vArr = new Array();
              vObj[name].forEach(function (item) {
                vArr.push(vThis.copyObject(item));
              });
              vObj[name] = vArr;
            }
          }
        }
      } else vObj = object;
      return vObj;
    },
    timestamp: function () {
      return this.datetime().format('yyyyMMddhhmmss');
    },
    serialId: function (mark) {
      return (mark ? mark + '_' : '') + 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('')[Math.floor(Math.random() * 26)] + this.datetime().time + Math.floor(Math.random() * 10000);
    },
    uuid: function () {
      var fRandom = function () {
        return (((1 + Math.random()) * 65536) | 0).toString(16).substring(1)
      };
      return [fRandom(), fRandom(), fRandom(), fRandom(), fRandom(), fRandom(), fRandom(), fRandom()].join('')
    },
    cryptoDes: function (text, key) {
      if (window.CryptoJS) {
        return CryptoJS.TripleDES.encrypt(text, CryptoJS.enc.Utf8.parse(key), {
          mode: CryptoJS.mode.ECB,
          padding: CryptoJS.pad.Pkcs7
        }).toString()
      }
      return ''
    },
    decodeDES: function(token, key){
      return CryptoJS.enc.Utf8.stringify(CryptoJS.TripleDES.decrypt(token, CryptoJS.enc.Utf8.parse(key), {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
      })).toString();
    },
    request: function (option) {
      var vXHR = null;
      var vConfig = Object.assign({
        type: 'GET',
        timeout: 2 * 60 * 1000
      }, option);
      var vXML = window.XMLHttpRequest || window.ActiveXObject;
      var vEvents = {
        readystatechange: function (e) {
          if (this.readyState === 4) {
            if (this.status === 200) {
              fCallback('success', this.response);
            } else fCallback('error', {messge: '网络请求结果失败', data: this.response});
          }
        },
        start: function (e) {
          fCallback('start', e);
        },
        timeout: function (e) {
          fCallback('timeout', {message: '网络请求超时'});
        },
        loadend: function (e) {
          fCallback('complete', e);
        },
        abort: function (e) {
          fCallback('abort', {message: '网络请求终止'});
        }
      };
      var fCallback = function (name, data) {
        if (name in vConfig) vConfig[name].call(vXHR, data);
      };
      var fBindEvent = function (target, events) {
        for (var name in events) {
          target.addEventListener(name, events[name], false);
        }
      };
      Object.assign(vConfig, option);
      if (vXML) {
        try {
          vXHR = new vXML();
          vXHR.open(vConfig.type, vConfig.url, true);
          fBindEvent(vXHR, vEvents);
          vXHR.timeout = vConfig.timeout;
          if (vConfig.dataType) vXHR.responseType = vConfig.dataType;
          if (vConfig.header) {
            for (var name in vConfig.header) {
              vXHR.setRequestHeader(name, vConfig.header[name]);
            }
          }
          vXHR.send(vConfig.data);
        } catch (e) {
          fCallback('error', e);
        }
      } else fCallback('error', new ErrorEvent());
      return vXHR;
    },
    loadResource: function(url){
      return this.promise(function(ok, fail){
        var reader = function(e){
          var file = new FileReader();
          file.onload = function(){
            ok({
              type: e.type,
              size: e.size,
              base64: this.result
            });
          };
          file.onerror = function(e){
            fail({message: '读取二进制失败'});
          };
          file.readAsDataURL(e);
        };
        if(this.isString(url)){
          this.request({
            url: url,
            dataType: 'blob',
            success: function(e){
              if(/octet-stream/.test(e.type)) {
                  ok({
                    type: e.type,
                    size: e.size,
                    base64: URL.createObjectURL(e)
                  })
              }else reader(e);
            },
            error: fail,
            abort: fail,
            timeout: fail
          });
        }else if (url instanceof File || url instanceof Blob) {
          //文件或二进制资源
          reader(url);
        }else fail({message: '不支持'});
      }.bind(this));
    },
    downloadResource: function(url, name){
      return this.promise(function(ok, fail){
        var fileName = name||this.datetime().format('yyyyMMddhhmmss');
        var link = document.createElement("a");
        if ("download" in link) {
          link.download = fileName;
          link.style.display = "none";
          link.href = url;
          document.body.appendChild(link);
          link.click();
          URL.revokeObjectURL(link.href);
          document.body.removeChild(link);
          ok(url);
        }else fail({message: '不支持下载'});
      }.bind(this))
    },
    datetime: function (date) {
      if (!date) date = new Date();
      else {
        if (this.isNumber(date) || this.isString(date)) {
          try {
            date = new Date(date);
          } catch (e) {
            date = new Date();
          }
        }
      }
      var fFitZero = function (a) {
        return a>9?a:'0'+a;
      };
      return {
        year: date.getFullYear(),
        month: date.getMonth() + 1,
        day: date.getDate(),
        week: date.getDay(),
        hour: date.getHours(),
        minute: date.getMinutes(),
        second: date.getSeconds(),
        time: date.getTime(),
        milltime: date.getMilliseconds(),
        date: date,
        format: function (pattern) {
          pattern = pattern || 'yyyy-MM-dd hh:mm:ss';
          var reg = pattern.match(/yyyy|MM|dd|hh|mm|ss/g);
          var value = {
            yyyy: this.year,
            MM: fFitZero(this.month),
            dd: fFitZero(this.day),
            hh: fFitZero(this.hour),
            mm: fFitZero(this.minute),
            ss: fFitZero(this.second)
          };
          if (reg) {
            reg.forEach(function (item) {
              pattern = pattern.replace(item, value[item]);
            });
          }
          return pattern;
        }
      }
    },
    formatTimer: function (time, flag) {
      var fCount = function (a) {
        return Math.floor(a);
      };
      var fFitZero = function (a) {
        return a>9?a:'0'+a;
      };
      var h = fCount(time / 3600),
        m = fCount(time % 3600 / 60),
        s = fCount(time % 3600 % 60);
      if (h == 0 && !flag) h = '';
      else h = fFitZero(h) + ':';
      return h + [fFitZero(m), fFitZero(s)].join(':');
    },
    each: function (arr, callback) {
      var flag;
      if (this.isArray(arr)) {
          for (var i = 0; i < arr.length; i++) {
              flag = callback(arr[i], i, arr);
              if (flag == false) return;
          }
      } else if (this.isObject(arr)) {
          for (var key in arr) {
              flag = callback(key, arr[key], arr);
              if (flag == false) return;
          }
      }
    },
    extend: function () {
      var options, name, src, copy, copyIsArray, clone,
        target = arguments[0] || {},
        i = 1,
        length = arguments.length,
        deep = false;
      var isPlainObject = function () {
        var proto, ctor;
        if (!obj || {}.toString.call(obj) !== "[object Object]") {
          return false;
        }
        proto = Object.getPrototypeOf(obj);
        if (!proto) {
          return true;
        }
        ctor = {}.hasOwnProperty.call(proto, "constructor") && proto.constructor;
        return typeof ctor === "function" && fnToString(ctor) === fnToString(Object);
      };
      var fnToString = function () {
        return {}.hasOwnProperty.toString.call;
      };
      if (typeof target === "boolean") {
        deep = target;
        target = arguments[i] || {};
        i++;
      }
      if (typeof target !== "object" && typeof target !== "function") {
        target = {};
      }
      if (i === length) {
        target = this;
        i--;
      }
      for (; i < length; i++) {
        if ((options = arguments[i]) != null) {
          for (name in options) {
            src = target[name];
            copy = options[name];
            if (name === "__proto__" || target === copy) {
              continue;
            }
            if (deep && copy && (isPlainObject(copy) ||
                (copyIsArray = Array.isArray(copy)))) {
              if (copyIsArray) {
                copyIsArray = false;
                clone = src && Array.isArray(src) ? src : [];
              } else {
                clone = src && isPlainObject(src) ? src : {};
              }
              target[name] = this.extend(deep, clone, copy);
            } else if (copy !== undefined) {
              target[name] = copy;
            }
          }
        }
      }
      return target;
    },
    argToArray: function () {
      return Array.prototype.slice.apply(arguments[0]);
    },
    argToObject: function (url) {
      var tObj = {};
      if (url && url.length > 0) {
        var tIndex = url.indexOf('?');
        if (tIndex != -1) {
          url = url.substr(tIndex + 1);
          var tArr = url.split('&');
          tArr.forEach(function (item) {
            if (item.indexOf('=') != -1) {
              var tItem = item.split('=');
              tObj[tItem[0]] = tItem.length > 1 ? tItem[1] : undefined;
            }
          })
        }
      }
      return tObj;
    },
    observer: function (context, object) {
      var tThis = this;
      this.each(object, function (name, data) {
        object[name] = (function (fn) {
          return function () {
            fn.apply(context, tThis.argToArray(arguments));
          }
        })(data);
      });
    },
    bindEvent: function (target, type, callback, data) {
      if (target && type && callback) {
        if (target.addEventListener) {
          var tCallback = function (e) {
            callback.call(this, e, this?this._cacheData:{});
          };
          target._cacheData = data;
          target.addEventListener(type, tCallback, false);
        }
        return {
          target: target,
          type: type,
          callback: tCallback
        }
      }
      return null;
    },
    unbindEvent: function (target, type, callback) {
      if (target) {
        if (target.target) {
          target.target.removeEventListener(target.type, target.callback, false);
        } else {
          if (type) {
            target.removeEventListener(type, callback, false);
          }
        }
      }
    },
    promise: function (callback, target) {
        return this.tryCatch(function(resolve, reject){
            callback.call(target||window, resolve, function(e){
                reject(this.isString(e) ? new Error(e) : e);
            }.bind(this));
        }.bind(this));
    },
    tryCatch: function (callback, target) {
        return new Promise(function(resolve, reject){
            try {
                callback.call(target||window, resolve, function(e){
                    reject(this.isString(e) ? new Error(e) : e);
                }.bind(this));
            } catch (e) {
                return reject && reject(e);
            }
        }.bind(this));
    }
  }, {
    REGEXP: {
      split: /\|/,
      reason: /\$\{reason\}/
    },
    TIMER_EVENT: 'start|stop|pause|resume|error|run|finish',
    SERVER_CONFIG: 'url|account|sender|deskey|desPassword',
    SERVER_EVENT: 'start|stop|ready|fail|error|process|online|offline|list|userStatus|network|networkType|networkData|callStatus|state',
    VIDEOCHAT_EVENT: 'ring|hangup|refuse|fail|error|timeout|connect|timer|waiting|stats|mediaStatus',
    AUDIOCALL_EVENT: 'ring|hangup|refuse|fail|error|timeout|connect|timer|waiting|stats|mediaStatus',
    NETVIDEOCHAT_EVENT: 'ring|hangup|refuse|fail|error|timeout|connect|timer|waiting|stats|mediaStatus|track',
    VOLTECALL_EVENT: 'ring|hangup|refuse|fail|error|timeout|connect|timer|waiting|stats|mediaStatus|track|callTypeChange|remoteVideoState|remoteAudioState',
    WHITEBOARD_EVENT: '',
    PEER_EVENT: 'offer|answer|candidate|status|ready|fail|error|connect|videoState|audioState|disconnect',
    VIDEOMEETING_EVENT: 'invite|cancel|timeout|connect|timer|waiting|stats|fail|error|end|ready|list|join|leave|mainScreen|mediaStatus',
    KEYS: {
      TIMER: 'Timer',
      MEDIADEVICE: 'MediaDevice',
      PEERCONNECT: 'PeerConnection',
      STATS: 'Stats',
      SERVERMANAGER: 'ServerManager',
      VIDEOCHAT: 'VideoChat',
      AUDIOCALL: 'AudioCall',
      NETVIDEOCHAT: 'NetVideoChat',
      VIDEOMEETING: 'VideoMeeting',
      WHITEBOARD: 'whiteboard',
      VOLTECALL: 'VolteCall'
    },
    TIP: {
      Timer: {
        10001: '定时器启动',
        10002: '定时器停止',
        10003: '定时器暂停',
        10004: '定时器唤醒',
        10005: '定时器运行中',
        10006: '定时器异常',
        10007: '定时器结束'
      },
      MediaDevice: {
        10001: '开始启动媒体',
        10002: '启动媒体成功',
        10003: '启动媒体失败',
        10004: '检测媒体是否可用',
        10005: '检测到媒体正常',
        10006: '检测到媒体不可用',
        10007: '检测摄像头状态',
        10008: '检测麦克风状态',
        10009: '检测扬声器状态',
        10010: '媒体状态'
      },
      PeerConnection: {
        10001: '开始创建通道',
        10002: '创建通道成功',
        10003: '创建通道失败',
        10004: '通道就绪',
        10005: '创建offer成功',
        10006: '创建offer失败',
        10007: '创建answer成功',
        10008: '创建answer失败',
        10009: '设置远程描述成功',
        10010: '设置远程描述失败',
        10011: '添加candidate成功',
        10012: '添加candidate失败',
        10013: '通道接通',
        10014: '通道状态',
        10015: '生成candidate',
        10016: '缓存candidate',
        10017: '画面状态',
        10018: '声音状态',
        10019: '通道断开'
      },
      Stats: {
        30000: '获取统计信息成功',
        30001: '获取统计信息失败'
      },
      ServerManager: {
        10001: '检测配置项',
        10002: '缺少配置项',
        10003: '鉴权成功',
        10004: '鉴权失败',
        10005: '连接服务正常',
        10006: '连接服务失败',
        10007: '服务就绪',
        10008: '服务关闭',
        10009: '服务状态',
        10010: '在线用户列表',
        10011: '用户上线',
        10012: '用户下线',
        10013: '用户状态',
        10014: '服务重连',
        10015: '服务重连失败',
        10016: '服务被强制下线',
        10017: '服务重连中',
        10018: '动态数据透传',
        10019: '服务已断开',
        10020: '网络断开了',
        10021: '网络连接上了',
        10022: '服务未断网络已断',
        10023: '通话状态发生变化',
        10024: '网络数据',
        10025: '网络类型'
      },
      VideoChat: {
        10001: '视频通话呼叫失败',
        10002: '视频通话响应失败',
        10003: '主动挂断失败',
        10004: '交换candidate失败',
        10005: '对方离线',
        10006: '对方拒接',
        10007: '对方繁忙',
        10008: '音频断开',
        10009: '对方未响应',
        10010: '对方被迫下线',
        10011: '通话已挂断',
        10012: '已在通话中',
        10013: '您有新来电',
        10014: '通话连接成功',
        10020: '正在呼叫对方',
        10021: '正在启动媒体',
        10022: '启动媒体成功',
        10023: '正在创建通道',
        10024: '通道就绪成功',
        10025: '正在发送offer',
        10026: '正在发送answer',
        10027: '正在发送candidate',
        10028: '设置远程描述失败'
      },
      AudioCall: {
        10001: '音频通话呼叫失败',
        10002: '音频通话响应失败',
        10003: '主动挂断失败',
        10004: '交换candidate失败',
        10005: '对方离线',
        10006: '对方拒接',
        10007: '对方繁忙',
        10008: '音频断开',
        10009: '对方未响应',
        10010: '对方被迫下线',
        10011: '通话已挂断',
        10012: '已在通话中',
        10013: '您有新来电',
        10014: '通话连接成功',
        10015: '号码或用户不能为空',
        10016: '重连通话失败',
        10020: '正在呼叫对方',
        10021: '正在启动媒体',
        10022: '启动媒体成功',
        10023: '正在创建通道',
        10024: '通道就绪成功',
        10025: '正在发送offer',
        10026: '正在发送answer',
        10027: '正在发送candidate',
        10030: '发送DTMF失败',
        10028: '设置远程描述失败'
      },
      NetVideoChat: {
        10001: '网络视频通话呼叫失败',
        10002: '网络视频通话响应失败',
        10003: '主动挂断失败',
        10004: '交换candidate失败',
        10005: '对方离线',
        10006: '对方拒接',
        10007: '对方繁忙',
        10008: '音频断开',
        10009: '对方未响应',
        10010: '对方被迫下线',
        10011: '通话已挂断',
        10012: '已在通话中',
        10013: '您有新来电',
        10014: '通话连接成功',
        10020: '正在呼叫对方',
        10021: '正在启动媒体',
        10022: '启动媒体成功',
        10023: '正在创建通道',
        10024: '通道就绪成功',
        10025: '正在发送offer',
        10026: '正在发送answer',
        10027: '正在发送candidate',
        10029: '轨道状态',
        10028: '设置远程描述失败',
        10029: '视频降级失败',
        10030: '语音升级失败'
      },
      VolteCall: {
        10001: 'Volte通话呼叫失败',
        10002: 'Volte通话响应失败',
        10003: '主动挂断失败',
        10004: '交换candidate失败',
        10005: '对方离线',
        10006: '对方拒接',
        10007: '对方繁忙',
        10008: '音频断开',
        10009: '对方未响应',
        10010: '对方被迫下线',
        10011: '通话已挂断',
        10012: '已在通话中',
        10013: '您有新来电',
        10014: '通话连接成功',
        10020: '正在呼叫对方',
        10021: '正在启动媒体',
        10022: '启动媒体成功',
        10023: '正在创建通道',
        10024: '通道就绪成功',
        10025: '正在发送offer',
        10026: '正在发送answer',
        10027: '正在发送candidate',
        10029: '轨道状态',
        10028: '设置远程描述失败',
        10030: '发送DTMF失败',
        10031: '远端画面状态',
        10032: '远端声音状态'
      },
      VideoMeeting: {
        10001: '正在创建会议',
        10002: '创建会议成功',
        10003: '创建会议失败',
        10004: '开始绑定会议',
        10005: '绑定会议成功',
        10006: '绑定会议失败',
        10007: '正在邀请成员',
        10008: '成功邀请成员',
        10009: '失败邀请成员',
        10010: '成员加入会议',
        10011: '成员离开会议',
        10012: '会议成员列表',
        10013: '会议就绪',
        10014: '邀请被取消',
        10015: '邀请超时',
        10016: '对方取消邀请',
        10017: '主屏切换',
        10018: '会议结束',
        10021: '启动媒体成功',
        10022: '启动媒体成功',
        10023: '正在创建通道',
        10024: '通道就绪成功',
        10025: '正在发送offer',
        10026: '正在发送answer',
        10027: '正在发送candidate',
        10028: '设置远程描述失败'
      },
      Whiteboard: {
        10001: '清空画板'
      }
    },
    STATE: {
      ok: '000',
      fail: -1,
      free: 'normal'
    },
    DEFAULTNOVIDEOIMG: 'data:image/png;base64,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'
  })
});