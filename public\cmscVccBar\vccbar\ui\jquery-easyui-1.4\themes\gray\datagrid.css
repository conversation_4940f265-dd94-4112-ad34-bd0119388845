.datagrid .panel-body {
  overflow: hidden;
  position: relative;
}
.datagrid-view {
  position: relative;
  overflow: hidden;
}
.datagrid-view1,
.datagrid-view2 {
  position: absolute;
  overflow: hidden;
  top: 0;
}
.datagrid-view1 {
  left: 0;
}
.datagrid-view2 {
  right: 0;
}
.datagrid-mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.3;
  filter: alpha(opacity=30);
  display: none;
}
.datagrid-mask-msg {
  position: absolute;
  top: 50%;
  margin-top: -20px;
  padding: 10px 5px 10px 30px;
  width: auto;
  height: 16px;
  border-width: 2px;
  border-style: solid;
  display: none;
}
.datagrid-sort-icon {
  padding: 0;
}
.datagrid-toolbar {
  height: auto;
  padding: 1px 2px;
  border-width: 0 0 1px 0;
  border-style: solid;
}
.datagrid-btn-separator {
  float: left;
  height: 24px;
  border-left: 1px solid #ccc;
  border-right: 1px solid #fff;
  margin: 2px 1px;
}
.datagrid .datagrid-pager {
  display: block;
  margin: 0;
  border-width: 1px 0 0 0;
  border-style: solid;
}
.datagrid .datagrid-pager-top {
  border-width: 0 0 1px 0;
}
.datagrid-header {
  overflow: hidden;
  cursor: default;
  border-width: 0 0 1px 0;
  border-style: solid;
}
.datagrid-header-inner {
  float: left;
  width: 10000px;
}
.datagrid-header-row,
.datagrid-row {
  height: 25px;
}
.datagrid-header td,
.datagrid-body td,
.datagrid-footer td {
  border-width: 0 1px 1px 0;
  border-style: dotted;
  margin: 0;
  padding: 0;
}
.datagrid-cell,
.datagrid-cell-group,
.datagrid-header-rownumber,
.datagrid-cell-rownumber {
  margin: 0;
  padding: 0 4px;
  white-space: nowrap;
  word-wrap: normal;
  overflow: hidden;
  height: 18px;
  line-height: 18px;
  font-size: 12px;
}
.datagrid-header .datagrid-cell {
  height: auto;
}
.datagrid-header .datagrid-cell span {
  font-size: 12px;
}
.datagrid-cell-group {
  text-align: center;
}
.datagrid-header-rownumber,
.datagrid-cell-rownumber {
  width: 25px;
  text-align: center;
  margin: 0;
  padding: 0;
}
.datagrid-body {
  margin: 0;
  padding: 0;
  overflow: auto;
  zoom: 1;
}
.datagrid-view1 .datagrid-body-inner {
  padding-bottom: 20px;
}
.datagrid-view1 .datagrid-body {
  overflow: hidden;
}
.datagrid-footer {
  overflow: hidden;
}
.datagrid-footer-inner {
  border-width: 1px 0 0 0;
  border-style: solid;
  width: 10000px;
  float: left;
}
.datagrid-row-editing .datagrid-cell {
  height: auto;
}
.datagrid-header-check,
.datagrid-cell-check {
  padding: 0;
  width: 27px;
  height: 18px;
  font-size: 1px;
  text-align: center;
  overflow: hidden;
}
.datagrid-header-check input,
.datagrid-cell-check input {
  margin: 0;
  padding: 0;
  width: 15px;
  height: 18px;
}
.datagrid-resize-proxy {
  position: absolute;
  width: 1px;
  height: 10000px;
  top: 0;
  cursor: e-resize;
  display: none;
}
.datagrid-body .datagrid-editable {
  margin: 0;
  padding: 0;
}
.datagrid-body .datagrid-editable table {
  width: 100%;
  height: 100%;
}
.datagrid-body .datagrid-editable td {
  border: 0;
  margin: 0;
  padding: 0;
}
.datagrid-view .datagrid-editable-input {
  margin: 0;
  padding: 2px 4px;
  border: 1px solid #D3D3D3;
  font-size: 12px;
  outline-style: none;
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.datagrid-sort-desc .datagrid-sort-icon {
  padding: 0 13px 0 0;
  background: url('images/datagrid_icons.png') no-repeat -16px center;
}
.datagrid-sort-asc .datagrid-sort-icon {
  padding: 0 13px 0 0;
  background: url('images/datagrid_icons.png') no-repeat 0px center;
}
.datagrid-row-collapse {
  background: url('images/datagrid_icons.png') no-repeat -48px center;
}
.datagrid-row-expand {
  background: url('images/datagrid_icons.png') no-repeat -32px center;
}
.datagrid-mask-msg {
  background: #ffffff url('images/loading.gif') no-repeat scroll 5px center;
}
.datagrid-header,
.datagrid-td-rownumber {
  background-color: #fafafa;
  background: -webkit-linear-gradient(top,#fdfdfd 0,#f5f5f5 100%);
  background: -moz-linear-gradient(top,#fdfdfd 0,#f5f5f5 100%);
  background: -o-linear-gradient(top,#fdfdfd 0,#f5f5f5 100%);
  background: linear-gradient(to bottom,#fdfdfd 0,#f5f5f5 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#fdfdfd,endColorstr=#f5f5f5,GradientType=0);
}
.datagrid-cell-rownumber {
  color: #000000;
}
.datagrid-resize-proxy {
  background: #bfbfbf;
}
.datagrid-mask {
  background: #ccc;
}
.datagrid-mask-msg {
  border-color: #D3D3D3;
}
.datagrid-toolbar,
.datagrid-pager {
  background: #fafafa;
}
.datagrid-header,
.datagrid-toolbar,
.datagrid-pager,
.datagrid-footer-inner {
  border-color: #ddd;
}
.datagrid-header td,
.datagrid-body td,
.datagrid-footer td {
  border-color: #ccc;
}
.datagrid-htable,
.datagrid-btable,
.datagrid-ftable {
  color: #000000;
  border-collapse: separate;
}
.datagrid-row-alt {
  background: #fafafa;
}
.datagrid-row-over,
.datagrid-header td.datagrid-header-over {
  background: #e2e2e2;
  color: #000000;
  cursor: default;
}
.datagrid-row-selected {
  background: #0092DC;
  color: #fff;
}
.datagrid-row-editing .textbox,
.datagrid-row-editing .textbox-text {
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
