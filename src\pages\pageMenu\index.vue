<!--
 * @Description: 城市运行管理服务系统主页
 * @Version: 2.0
 * @Autor: wjb
 * @Date: 2025-06-19 08:32:42
 * @LastEditors: wjb
 * @LastEditTime: 2025-06-24 08:24:05
-->
<template>
  <div class="choosePage" id="choosePage">
    <div class="name">
      {{ city + '城市运行管理服务系统' }}
    </div>
    <div class="tops">
      <div
        class="topItem"
        v-for="(item, i) in topArr"
        :key="i"
        @click="pageJump(item)"
        :style="{ background: 'url(' + item.back + ')' }"
      ></div>
    </div>

    <!-- 数据可视化大屏布局 -->
    <div class="dashboard-container">
      <!-- 左侧面板 -->
      <div class="left-panel">
        <div class="panel-header">
          <div class="panel-title">执法态势分析</div>
          <div class="panel-subtitle">实时监控</div>
        </div>

        <!-- 功能菜单区域 -->
        <div class="function-menu">
          <div class="menu-section">
            <h3>主要功能</h3>
            <div class="menu-grid">
              <div
                v-for="(item, index) in leftMenuItems"
                :key="index"
                class="menu-item"
                @click="pageJump(item)">
                <img :src="item.icon" alt="" />
                <span>{{ item.name }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 数据统计卡片 -->
        <div class="stats-cards">
          <div class="stat-card" v-for="(stat, index) in leftStats" :key="index">
            <div class="stat-icon">
              <i :class="stat.icon"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-label">{{ stat.label }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中央主屏 -->
      <div class="center-panel">
        <div class="center-header">
          <div class="main-title">{{ city }}城市运行管理服务系统</div>
          <div class="time-info">{{ currentTime }}</div>
        </div>

        <!-- 中央地图/主要内容区域 -->
        <div class="main-content">
          <div class="map-container">
            <img class="main-bg" src="@/assets/home/<USER>" alt="城市地图" />

            <!-- 旋转菜单 -->
            <div class="rotating-menu">
              <div class="city-swipper-item"
                   v-for="(item, index) in curvedMenuItems"
                   :key="index"
                   @click="pageJump(item)">
                <img :src="item.icon" alt="" />
                <div class="item-label">{{ item.name }}</div>
              </div>
            </div>
          </div>

          <!-- 底部数据面板 -->
          <div class="bottom-data-panel">
            <div class="data-item" v-for="(data, index) in centerData" :key="index">
              <div class="data-title">{{ data.title }}</div>
              <div class="data-value">{{ data.value }}</div>
              <div class="data-trend" :class="data.trend">
                <i :class="data.trendIcon"></i>
                {{ data.trendValue }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧面板 -->
      <div class="right-panel">
        <div class="panel-header">
          <div class="panel-title">综合监管平台</div>
          <div class="panel-subtitle">数据分析</div>
        </div>

        <!-- 右侧功能菜单 -->
        <div class="function-menu">
          <div class="menu-section">
            <h3>监管功能</h3>
            <div class="menu-grid">
              <div
                v-for="(item, index) in rightMenuItems"
                :key="index"
                class="menu-item"
                @click="pageJump(item)">
                <img :src="item.icon" alt="" />
                <span>{{ item.name }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 实时数据展示 -->
        <div class="realtime-data">
          <div class="data-chart" v-for="(chart, index) in rightCharts" :key="index">
            <div class="chart-title">{{ chart.title }}</div>
            <div class="chart-content">
              <!-- 这里可以集成图表组件 -->
              <div class="mock-chart" :style="{ background: chart.color }">
                <div class="chart-value">{{ chart.value }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getUrl, getSzcjToken, getUserArea, loginByThirdComplete } from '@/api/indexApi'
import mainObj from '@/utils/PocMain.js'
import { Encrypt } from '@/utils/Encrypt'
// 导入本地增强版走马灯组件
import EnhancedCarousel, { EnhancedCarouselItem } from '@/components/carousel/enhanced-carousel'

export default {
  name: 'index',
  components: {
    EnhancedCarousel,
    EnhancedCarouselItem
  },
  data() {
    return {
      topArr: [
        // {
        //   back: require('@/assets/login/login_csyxgl.png'),
        //   name: '首页',
        //   key: 'csyxgl',
        // },
        {
          back: require('@/assets/login/login_admin.png'),
          name: '后台管理',
        },
      ],
      bannerList: [
        {
          url: require('@/assets/home/<USER>'),
          name: '左侧',
        },
        {
          url: require('@/assets/home/<USER>'),
          name: '右侧',
        },
        {
          url: require('@/assets/home/<USER>'),
          name: '左侧',
        },
        {
          url: require('@/assets/home/<USER>'),
          name: '右侧',
        },
      ],
      // 贝塞尔曲线排列的7个主要功能图标
      i: 0,
      commInterval: null,
      DIS: null,
      curvedMenuItems: [
        {
          icon: require('@/assets/home/<USER>'),
          name: '行政执法一张图',
          key: 'home',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '执法态势一张图',
          key: 'zfts',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '指挥调度一张图',
          key: 'zhdd',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '三色预警一张图',
          key: 'ssyj',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '行政执法综合评价一张图',
          key: 'xxzf_zhpj',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '案件回访',
          key: 'ajhf',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '县级中心',
          key: 'xjzx',
        },
      ],
      // 左侧菜单项目
      leftMenuItems: [
        {
          icon: require('@/assets/home/<USER>'),
          name: '行政执法一张图',
          key: 'home',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '执法态势一张图',
          key: 'zfts',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '指挥调度一张图',
          key: 'zhdd',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '三色预警一张图',
          key: 'ssyj',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '行政执法综合评价一张图',
          key: 'xxzf_zhpj',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '案件回访',
          key: 'ajhf',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '县级中心',
          key: 'xjzx',
        },
      ],
      // 右侧菜单项目
      rightMenuItems: [
        {
          icon: require('@/assets/home/<USER>'),
          name: '城市运行管理服务首页',
          key: 'csyxgl',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '城市管理一张图',
          key: 'csgl',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '物联网设备一张图',
          key: 'wlwsb',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '垃圾分类一张图',
          key: 'ljfl',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '公众服务一张图',
          key: 'gzfw',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '综合评价一张图',
          key: 'zhpj',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '运行监测一张图',
          key: 'yxjc',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '犬类管理一张图',
          key: 'dog',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '站前执法一张图',
          key: 'zqyzt',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '数字城建一张图',
          key: 'szcj',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '应用集成',
          key: 'yyjc',
        },
      ],
      szcjToken: '',
    }
  },
  computed: {
    city() {
      return localStorage.getItem('adminCity')
    },
  },
  mounted() {
    // 初始化 POC

    try {
      mainObj.init()

      // 然后初始化视频数据
      this.initPocVideoData()
    } catch (error) {
      console.error('POC 初始化失败:', error)
    }
    clearInterval(this.commInterval)
    this.initOptimizedRotation()
    this.setupEnhancedMouseEvents()
    this.getArea()
    this.getSzcjToken()
  },
  beforeDestroy() {
    // 清理旋转动画定时器
    clearInterval(this.commInterval)
  },
  methods: {
    /**
     * 优化的圆形旋转函数 - 更大的旋转圈宽度
     */
    funNcl() {
      const menuElements = document.getElementsByClassName('city-swipper-item')
      const elementCount = menuElements.length

      if (elementCount === 0) return

      // 优化的配置参数 - 更大的旋转圈
      const config = {
        containerWidth: 3600,
        containerHeight: 1600,
        radiusX: 1700, // 水平半径增大 (原来是 width/4 = 750，现在是 600)
        radiusY: 400, // 垂直半径增大 (原来是 height/8 = 200，现在是 300)
        speed: -0.008, // 稍微调整速度以适应更大的圈
        centerX: 1800, // containerWidth / 2
        centerY: 800, // containerHeight / 2
      }

      // 计算每个元素之间的角度间隔
      const angleInterval = (2 * Math.PI) / elementCount

      for (let index = 0; index < elementCount; index++) {
        const element = menuElements[index]
        const elementStyle = element.style

        elementStyle.position = 'absolute'

        // 计算当前角度
        const currentAngle = this.i * config.speed + index * angleInterval

        // 使用椭圆轨道公式，创建更大的旋转圈
        const x = Math.sin(currentAngle) * config.radiusX + config.centerX
        const y = Math.cos(currentAngle) * config.radiusY + config.centerY

        // 应用位置
        elementStyle.left = x + 'px'
        elementStyle.top = y + 'px'

        // 添加深度效果 - 根据Y位置调整透明度和缩放
        const depthFactor = (Math.cos(currentAngle) + 1) / 2 // 0 到 1 之间
        const scale = 0.8 + depthFactor * 0.4 // 0.8 到 1.2 之间的缩放
        const opacity = 0.7 + depthFactor * 0.3 // 0.7 到 1.0 之间的透明度

        elementStyle.transform = `scale(${scale})`
        elementStyle.opacity = opacity
        elementStyle.zIndex = Math.floor(depthFactor * 10) + 1
      }

      this.i++
    },
    changeCard(i) {
      console.log(i)
      if (i == 0 || i == 2) {
        this.curvedMenuItems = this.leftMenuItems
      } else {
        this.curvedMenuItems = this.rightMenuItems
      }
    },

    /**
     * 初始化优化的旋转动画
     */
    initOptimizedRotation() {
      // 使用更高的帧率以获得更流畅的动画
      this.commInterval = setInterval(() => {
        this.funNcl()
      }, 60) // 约16.7fps，更流畅
    },

    /**
     * 设置增强的鼠标事件
     */
    setupEnhancedMouseEvents() {
      const self = this

      $('.city-swipper-item').mouseenter(function () {
        // 暂停旋转
        if (self.commInterval) {
          clearInterval(self.commInterval)
          self.commInterval = null
        }

        // 添加悬停效果
        $(this).css({
          transform: 'scale(1.2)',
          'z-index': '100',
          transition: 'all 0.3s ease',
        })
      })

      $('.city-swipper-item').mouseleave(function () {
        // 恢复旋转
        if (!self.commInterval) {
          self.commInterval = setInterval(() => {
            self.funNcl()
          }, 60)
        }

        // 移除悬停效果
        $(this).css({
          transition: 'all 0.3s ease',
        })
      })
    },
    //获取用户所属区县
    getArea() {
      getUserArea().then((res) => {
        if (res.code == 200) {
          localStorage.setItem('city', res.data.area)
          localStorage.setItem('adminCity', res.data.area)
        }
      })
    },
    async initPocVideoData() {
      try {
        const authInfo = await mainObj.queryAuth()
        console.log('POC 认证信息:', authInfo)

        // 继续处理视频数据...
      } catch (error) {
        console.error('初始化 POC 视频数据失败:', error)
      }
    },
    getSzcjToken() {
      getSzcjToken({
        username: '13735713555',
        password: Encrypt('%&fyAB2%mZ'),
      }).then((res) => {
        console.log('szcjToken', res.token)
        this.szcjToken = res.token
      })
    },
    pageJump(item) {
      switch (item.name) {
        case '绩效评估':
          getUrl('/token/getTokenInfo', { jmppage: 'ks' }).then((res) => {
            if (res.code == 200) {
              this.openHtmlByMode(res.data.url)
            }
          })
          break
        case '三色预警一张图':
          getUrl({ type: 'dashboard', module: 'ssyj' }).then((res) => {
            if (res.code == 200) {
              this.openHtmlByMode(res.data.url)
            }
          })
          break
        case '行政执法综合评价一张图':
          getUrl({ type: 'dashboard', module: 'xzzfpjzb' }).then((res) => {
            if (res.code == 200) {
              this.openHtmlByMode(res.data.url)
            }
          })
          break
        case '首页':
          this.getPage(item)
          break
        case '驾驶舱':
          this.getPage(item)
          break
        case '指挥调度一张图':
          this.getPage(item)
          break
        case '执法态势一张图':
          this.getPage(item)
          break
        case '行政执法一张图':
          this.getPage(item)
          break
        case '城市管理一张图':
          this.getPage(item)
          break
        case '物联网设备一张图':
          this.getPage(item)
          break
        case '公众服务一张图':
          this.getPage(item)
          break
        case '综合评价一张图':
          this.getPage(item)
          break
        case '运行监测一张图':
          this.getPage(item)
          break
        case '犬类管理一张图':
          this.getPage(item)
          break
        case '站前执法一张图':
          this.getPage(item)
          break
        case '县级中心':
          this.getPage(item)
          break
        case '应用集成':
          this.getPage(item)
          break
        case '后台管理':
          this.openHtmlByMode(
            process.env.NODE_ENV === 'production'
              ? 'http://10.45.13.116/ygf/login'
              : 'http://10.45.13.116:8000/ygf-web/login'
          )
          break
        case '案件回访':
          this.getPage(item)
          break
        case '数字城建一张图':
          this.openHtmlByMode('https://jhqy.jsj.jinhua.gov.cn/dplus/view/1672878871176626178?token=' + this.szcjToken)
        case '垃圾分类一张图':
          loginByThirdComplete({
            tenantCode: 'TENANT_JHCC',
            account: 'dddl',
            scope: 'THIRD_APP',
            appKey: '25jhljfl05ygf29',
            appSecret: 'B7FEAAEAD3AD3ED37468A45EF8030E94',
          }).then((res) => {
            if (res.msg == '登录成功') {
              if (res.data.access_token) {
                let access_token = res.data.access_token
                this.openHtmlByMode(`http://ljfl.xzzfj.jinhua.gov.cn/#/autoLogin?token=${access_token}`)
              }
            }
          })
          break
      }
    },
    getPage(item) {
      this.$router.push('/' + item.key)
    },
    openHtmlByMode(url) {
      window.open(url)
    },
    //获取端口号
    getCurrentPortWithDefault() {
      let port = window.location.port
      if (port === '') {
        if (window.location.protocol === 'http:') {
          port = '80'
        } else if (window.location.protocol === 'https:') {
          port = '443'
        }
      }
      return port
    },

    /**
     * 增强版走马灯切换事件处理
     */
    changeCard(current, prev) {
      console.log('Enhanced Carousel changed from', prev, 'to', current)
      // 可以在这里添加切换时的自定义逻辑
    },

    /**
     * 手动切换到指定的走马灯项
     */
    switchToCarouselItem(index) {
      if (this.$refs.enhancedCarousel) {
        this.$refs.enhancedCarousel.setActiveItem(index)
      }
    },

    /**
     * 获取当前激活的走马灯项索引
     */
    getCurrentCarouselIndex() {
      if (this.$refs.enhancedCarousel) {
        return this.$refs.enhancedCarousel.activeIndex
      }
      return 0
    },
  },
  watch: {},
}
</script>

<style lang="less" scoped>
.choosePage {
  width: 100%;
  height: 100%;
  background: url('@/assets/home/<USER>');
  background-size: cover;
  background-position: center;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.name {
  width: 100%;
  text-align: center;
  font-size: 64px;
  font-family: YouSheBiaoTiHei;
  font-weight: 400;
  color: #e3f3ff;
  margin-top: 70px;
  margin-left: 20px;
}

.tops {
  width: 100%;
  height: fit-content;
  box-sizing: border-box;
  display: flex;
  justify-content: right;
  position: absolute;
  top: 75px;
  right: 114px;
}
.topItem {
  width: 420px;
  height: 172px;
  margin-left: 80px;
  cursor: pointer;
  background-size: 100% 100% !important;
}

/* 主要内容容器 */
.main-container {
  flex: 1;
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
  .roud_bkg {
    width: 3387px;
    height: 701px;
    position: absolute;
    top: 771px;
  }
  .car_box {
    width: 2880px;
    height: 1000px;
    margin-top: 200px;
  }
  .wlgz-con {
    width: 3387px;
    height: 701px;
    display: flex;
    justify-content: space-around;
    .city-swipper-item {
      width: 263px;
      height: 317px;
      cursor: pointer;
      transition: all 0.3s ease;

      /* 优化性能 */
      will-change: transform, opacity, left, top;
      backface-visibility: hidden;

      img {
        width: 263px;
        height: 317px;
        border-radius: 8px;
        transition: all 0.3s ease;
      }

    }
  }
}

/* 增强版走马灯样式 - 本地组件 */
.car_box {
  /* 确保容器有足够空间显示侧边卡片 */
  overflow: visible;
  padding: 0 100px;

  .enhanced-carousel-item {
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);

    &.enhanced-mode.is-in-stage {
      cursor: pointer;
      opacity: 0.8;

      &:hover {
        opacity: 1;
        transform: scale(1.05) !important;
        z-index: 15;
      }
    }

    &.is-active {
      z-index: 20;
      opacity: 1;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      transition: all 0.4s ease;
    }

    &.is-active img {
      box-shadow: 0 12px 48px rgba(0, 0, 0, 0.4);
    }

    &.is-in-stage img {
      box-shadow: 0 6px 24px rgba(0, 0, 0, 0.25);
    }
  }

  .enhanced-mask {
    background: rgba(0, 0, 0, 0.2);
    transition: opacity 0.3s ease;
    border-radius: 12px;
  }

  .enhanced-carousel-item:hover .enhanced-mask {
    opacity: 0.1;
  }
}

/* 兼容原有的 Element UI 样式 */
.el-carousel__item--card.is-in-stage {
  z-index: 10;
}
.el-carousel__item--card.is-active {
  z-index: 20;
}
</style>
