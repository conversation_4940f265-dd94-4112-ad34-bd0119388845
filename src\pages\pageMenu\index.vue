<!--
 * @Description: 城市运行管理服务系统主页
 * @Version: 2.0
 * @Autor: wjb
 * @Date: 2025-06-19 08:32:42
 * @LastEditors: wjb
 * @LastEditTime: 2025-06-23 16:00:00
-->
<template>
  <div class="choosePage" id="choosePage">
    <div class="name">
      {{ city + '城市运行管理服务系统' }}
    </div>
    <div class="tops">
      <div
        class="topItem"
        v-for="(item, i) in topArr"
        :key="i"
        @click="pageJump(item)"
        :style="{ background: 'url(' + item.back + ')' }"
      ></div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-container">
      <img class="roud_bkg" src="@/assets/home/<USER>" alt="" />
      <div class="wlgz-con">
        <div class="city-swipper-item" v-for="(item, index) in curvedMenuItems" :key="index" @click="pageJump(item)">
          <img :src="item.icon" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getUrl, indexApi, getSzcjToken, getUserArea, loginByThirdComplete } from '@/api/indexApi'
import mainObj from '@/utils/PocMain.js'
import { Encrypt } from '@/utils/Encrypt'

export default {
  name: 'index',
  data() {
    return {
      topArr: [
        // {
        //   back: require('@/assets/login/login_csyxgl.png'),
        //   name: '首页',
        //   key: 'csyxgl',
        // },
        {
          back: require('@/assets/login/login_admin.png'),
          name: '后台管理',
        },
      ],
      // 贝塞尔曲线排列的7个主要功能图标
      i: 0,
      commInterval: null,
      DIS: null,
      curvedMenuItems: [
        {
          icon: require('@/assets/home/<USER>'),
          name: '行政执法一张图',
          key: 'home',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '执法态势一张图',
          key: 'zfts',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '指挥调度一张图',
          key: 'zhdd',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '三色预警一张图',
          key: 'ssyj',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '城市管理一张图',
          key: 'csgl',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '综合评价一张图',
          key: 'zhpj',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '运行监测一张图',
          key: 'yxjc',
        },
      ],
      // 左侧菜单项目
      leftMenuItems: [
        {
          name: '行政执法综合评价一张图',
          key: 'xxzf_zhpj',
        },
        {
          name: '案件回访',
          key: 'ajhf',
        },
        {
          name: '县级中心',
          key: 'xjzx',
        },
      ],
      // 右侧菜单项目
      rightMenuItems: [
        {
          name: '物联网设备一张图',
          key: 'wlwsb',
        },
        {
          name: '垃圾分类一张图',
          key: 'ljfl',
        },
        {
          name: '公众服务一张图',
          key: 'gzfw',
        },
        {
          name: '犬类管理一张图',
          key: 'dog',
        },
        {
          name: '站前执法一张图',
          key: 'zqyzt',
        },
        {
          name: '数字城建一张图',
          key: 'szcj',
        },
        {
          name: '应用集成',
          key: 'yyjc',
        },
      ],
      pageArr: [
        {
          img: require('@/assets/login/login_ssyj.png'),
          name: '三色预警一张图',
          key: 'ssyj',
        },
        {
          img: require('@/assets/login/login_xxzf_zhpj.png'),
          name: '行政执法综合评价一张图',
          key: 'xxzf_zhpj',
        },
        {
          img: require('@/assets/login/login_csgl.png'),
          name: '城市管理一张图',
          key: 'csgl',
        },
        {
          img: require('@/assets/login/login_wlwsb.png'),
          name: '物联网设备一张图',
          key: 'wlwsb',
        },
        {
          img: require('@/assets/login/login_ljfl.png'),
          name: '垃圾分类一张图',
          key: 'ljfl',
        },
        {
          img: require('@/assets/login/login_gzfw.png'),
          name: '公众服务一张图',
          key: 'gzfw',
        },
        {
          img: require('@/assets/login/login_ajhf.png'),
          name: '案件回访',
          key: 'ajhf',
        },
        {
          img: require('@/assets/login/login_yyjc.png'),
          name: '应用集成',
          key: 'yyjc',
        },
        // {
        //   img: require('@/assets/login/login_jxpg.png'),
        //   name: '绩效评估',
        //   key: 'jxpg',
        // },
        {
          img: require('@/assets/login/login_zhpj.png'),
          name: '综合评价一张图',
          key: 'zhpj',
        },
        {
          img: require('@/assets/login/login_yxjc.png'),
          name: '运行监测一张图',
          key: 'yxjc',
        },
        {
          img: require('@/assets/login/login_qlgl.png'),
          name: '犬类管理一张图',
          key: 'dog',
        },
        {
          img: require('@/assets/login/login_zqzf.png'),
          name: '站前执法一张图',
          key: 'zqyzt',
        },
        {
          img: require('@/assets/login/login_szcj.png'),
          name: '数字城建一张图',
          key: 'szcj',
        },
        {
          img: require('@/assets/login/login_xzzf.png'),
          name: '行政执法一张图',
          key: 'home',
        },
        {
          img: require('@/assets/login/login_zfts.png'),
          name: '执法态势一张图',
          key: 'zfts',
        },
        {
          img: require('@/assets/login/login_zhdd.png'),
          name: '指挥调度一张图',
          key: 'zhdd',
        },
        {
          img: require('@/assets/login/login_xjzx.png'),
          name: '县级中心',
          key: 'xjzx',
        },
      ],
      szcjToken: '',
    }
  },
  computed: {
    city() {
      return localStorage.getItem('adminCity')
    },
  },
  mounted() {
    // 初始化 POC

    try {
      mainObj.init()

      // 然后初始化视频数据
      this.initPocVideoData()
    } catch (error) {
      console.error('POC 初始化失败:', error)
    }
    // this.getPageMenu()
    let that = this
    that.commInterval = setInterval(() => {
      that.funNcl()
    }, 100)
    $('.city-swipper-item').mouseenter((e) => {
      clearInterval(that.commInterval)
      that.commInterval = null
    })
    $('.city-swipper-item').mouseleave((e) => {
      that.commInterval = setInterval(that.funNcl(), 100)
    })
    this.getArea()
    this.getSzcjToken()
  },
  methods: {
    funNcl() {
      var DI = document.getElementsByClassName('city-swipper-item')
      var DIL = DI.length
      var width = 3000
      var height = 701
      for (var index = 0; index < DIL; index++) {
        this.DIS = DI[index].style
        this.DIS.position = 'absolute'
        var speed = -0.01
        var interV = (2 * Math.PI) / speed / DIL
        this.DIS.left = Math.sin(this.i * speed + index * interV * speed) * (width / 4) + width / 2 + 'px'
        this.DIS.top = Math.cos(this.i * speed + index * interV * speed) * (height / 8) + height / 2 + 'px'
      }
      this.i++
    },
    //获取用户所属区县
    getArea() {
      getUserArea().then((res) => {
        if (res.code == 200) {
          localStorage.setItem('city', res.data.area)
          localStorage.setItem('adminCity', res.data.area)
        }
      })
    },
    async initPocVideoData() {
      try {
        const authInfo = await mainObj.queryAuth()
        console.log('POC 认证信息:', authInfo)

        // 继续处理视频数据...
      } catch (error) {
        console.error('初始化 POC 视频数据失败:', error)
      }
    },
    getPageMenu() {
      indexApi('/xzzfj_sy_cd', { area: localStorage.getItem('adminCity') }).then((res) => {
        this.pageArr = res.data
      })
    },
    getSzcjToken() {
      getSzcjToken({
        username: '13735713555',
        password: Encrypt('%&fyAB2%mZ'),
      }).then((res) => {
        console.log('szcjToken', res.token)
        this.szcjToken = res.token
      })
    },
    pageJump(item) {
      switch (item.name) {
        case '绩效评估':
          getUrl('/token/getTokenInfo', { jmppage: 'ks' }).then((res) => {
            if (res.code == 200) {
              this.openHtmlByMode(res.data.url)
            }
          })
          break
        case '三色预警一张图':
          getUrl({ type: 'dashboard', module: 'ssyj' }).then((res) => {
            if (res.code == 200) {
              this.openHtmlByMode(res.data.url)
            }
          })
          break
        case '行政执法综合评价一张图':
          getUrl({ type: 'dashboard', module: 'xzzfpjzb' }).then((res) => {
            if (res.code == 200) {
              this.openHtmlByMode(res.data.url)
            }
          })
          break
        case '首页':
          this.getPage(item)
          break
        case '驾驶舱':
          this.getPage(item)
          break
        case '指挥调度一张图':
          this.getPage(item)
          break
        case '执法态势一张图':
          this.getPage(item)
          break
        case '行政执法一张图':
          this.getPage(item)
          break
        case '城市管理一张图':
          this.getPage(item)
          break
        case '物联网设备一张图':
          this.getPage(item)
          break
        case '公众服务一张图':
          this.getPage(item)
          break
        case '综合评价一张图':
          this.getPage(item)
          break
        case '运行监测一张图':
          this.getPage(item)
          break
        case '犬类管理一张图':
          this.getPage(item)
          break
        case '站前执法一张图':
          this.getPage(item)
          break
        case '县级中心':
          this.getPage(item)
          break
        case '应用集成':
          this.getPage(item)
          break
        case '后台管理':
          this.openHtmlByMode(
            process.env.NODE_ENV === 'production'
              ? 'http://10.45.13.116/ygf/login'
              : 'http://10.45.13.116:8000/ygf-web/login'
          )
          break
        case '案件回访':
          this.getPage(item)
          break
        case '数字城建一张图':
          this.openHtmlByMode('https://jhqy.jsj.jinhua.gov.cn/dplus/view/1672878871176626178?token=' + this.szcjToken)
        case '垃圾分类一张图':
          loginByThirdComplete({
            tenantCode: 'TENANT_JHCC',
            account: 'dddl',
            scope: 'THIRD_APP',
            appKey: '25jhljfl05ygf29',
            appSecret: 'B7FEAAEAD3AD3ED37468A45EF8030E94',
          }).then((res) => {
            if (res.msg == '登录成功') {
              if (res.data.access_token) {
                let access_token = res.data.access_token
                this.openHtmlByMode(`http://ljfl.xzzfj.jinhua.gov.cn/#/autoLogin?token=${access_token}`)
              }
            }
          })
          break
      }
    },
    getPage(item) {
      this.$router.push('/' + item.key)
    },
    openHtmlByMode(url) {
      window.open(url)
    },
    //获取端口号
    getCurrentPortWithDefault() {
      let port = window.location.port
      if (port === '') {
        if (window.location.protocol === 'http:') {
          port = '80'
        } else if (window.location.protocol === 'https:') {
          port = '443'
        }
      }
      return port
    },
  },
  watch: {},
}
</script>

<style lang="scss" scoped>
.choosePage {
  width: 100%;
  height: 100%;
  background: url('@/assets/home/<USER>');
  background-size: cover;
  background-position: center;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.name {
  width: 100%;
  text-align: center;
  font-size: 64px;
  font-family: YouSheBiaoTiHei;
  font-weight: 400;
  color: #e3f3ff;
  margin-top: 70px;
  margin-left: 20px;
}

.tops {
  width: 100%;
  height: fit-content;
  box-sizing: border-box;
  display: flex;
  justify-content: right;
  position: absolute;
  top: 75px;
  right: 114px;
}
.topItem {
  width: 420px;
  height: 172px;
  margin-left: 80px;
  cursor: pointer;
  background-size: 100% 100% !important;
}

/* 主要内容容器 */
.main-container {
  flex: 1;
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  .roud_bkg {
    width: 3387px;
    height: 701px;
    position: absolute;
    top: 671px;
  }
  .wlgz-con {
    width: 3387px;
    height: 701px;
    display: flex;
    justify-content: space-around;
    .city-swipper-item {
      width: 263px;
      height: 317px;
      img {
        width: 263px;
        height: 317px;
      }
    }
  }
}
</style>
