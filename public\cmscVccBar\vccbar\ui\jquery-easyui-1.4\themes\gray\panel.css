.panel {
  overflow: hidden;
  text-align: left;
  margin: 0;
  border: 0;
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.panel-header,
.panel-body {
  border-width: 1px;
  border-style: solid;
}
.panel-header {
  padding: 5px;
  position: relative;
}
.panel-title {
  background: url('images/blank.gif') no-repeat;
}
.panel-header-noborder {
  border-width: 0 0 1px 0;
}
.panel-body {
  overflow: auto;
  border-top-width: 0;
  padding: 0;
}
.panel-body-noheader {
  border-top-width: 1px;
}
.panel-body-noborder {
  border-width: 0px;
}
.panel-body-nobottom {
  border-bottom-width: 0;
}
.panel-with-icon {
  padding-left: 18px;
}
.panel-icon,
.panel-tool {
  position: absolute;
  top: 50%;
  margin-top: -8px;
  height: 16px;
  overflow: hidden;
}
.panel-icon {
  left: 5px;
  width: 16px;
}
.panel-tool {
  right: 5px;
  width: auto;
}
.panel-tool a {
  display: inline-block;
  width: 16px;
  height: 16px;
  opacity: 0.6;
  filter: alpha(opacity=60);
  margin: 0 0 0 2px;
  vertical-align: top;
}
.panel-tool a:hover {
  opacity: 1;
  filter: alpha(opacity=100);
  background-color: #e2e2e2;
  -moz-border-radius: 3px 3px 3px 3px;
  -webkit-border-radius: 3px 3px 3px 3px;
  border-radius: 3px 3px 3px 3px;
}
.panel-loading {
  padding: 11px 0px 10px 30px;
}
.panel-noscroll {
  overflow: hidden;
}
.panel-fit,
.panel-fit body {
  height: 100%;
  margin: 0;
  padding: 0;
  border: 0;
  overflow: hidden;
}
.panel-loading {
  background: url('images/loading.gif') no-repeat 10px 10px;
}
.panel-tool-close {
  background: url('images/panel_tools.png') no-repeat -16px 0px;
}
.panel-tool-min {
  background: url('images/panel_tools.png') no-repeat 0px 0px;
}
.panel-tool-max {
  background: url('images/panel_tools.png') no-repeat 0px -16px;
}
.panel-tool-restore {
  background: url('images/panel_tools.png') no-repeat -16px -16px;
}
.panel-tool-collapse {
  background: url('images/panel_tools.png') no-repeat -32px 0;
}
.panel-tool-expand {
  background: url('images/panel_tools.png') no-repeat -32px -16px;
}
.panel-header,
.panel-body {
  border-color: #D3D3D3;
}
.panel-header {
  background-color: #f3f3f3;
  background: -webkit-linear-gradient(top,#F8F8F8 0,#eeeeee 100%);
  background: -moz-linear-gradient(top,#F8F8F8 0,#eeeeee 100%);
  background: -o-linear-gradient(top,#F8F8F8 0,#eeeeee 100%);
  background: linear-gradient(to bottom,#F8F8F8 0,#eeeeee 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#F8F8F8,endColorstr=#eeeeee,GradientType=0);
}
.panel-body {
  background-color: #ffffff;
  color: #000000;
  font-size: 12px;
}
.panel-title {
  font-size: 12px;
  font-weight: bold;
  color: #575765;
  height: 16px;
  line-height: 16px;
}
.panel-footer {
  border: 1px solid #D3D3D3;
  overflow: hidden;
  background: #fafafa;
}
.panel-footer-noborder {
  border-width: 1px 0 0 0;
}
